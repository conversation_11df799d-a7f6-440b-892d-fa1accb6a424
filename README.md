# 🔍 数据库查询系统 v2.0

一个基于Flask + Elasticsearch的智能数据查询系统，采用模块化架构设计。

## ✨ 主要特性

- 🏗️ **模块化架构** - 清晰的代码组织结构
- 🔧 **配置外部化** - 环境变量配置管理
- 🚀 **统一启动脚本** - 支持多种启动模式

## 🏗️ 项目结构

```
database_query_system/
├── app/                        # 应用核心代码
│   ├── __init__.py            # 应用包初始化
│   ├── main.py                # 主应用入口
│   ├── config/                # 配置模块
│   │   ├── __init__.py
│   │   └── settings.py        # 配置管理
│   ├── core/                  # 核心功能
│   │   ├── __init__.py
│   │   ├── error_handler.py   # 错误处理
│   │   ├── logger.py          # 日志配置
│   │   └── monitor.py         # 性能监控
│   ├── services/              # 业务服务
│   │   ├── __init__.py
│   │   ├── es_service.py      # ES服务
│   │   └── record_service.py  # 记录服务
│   └── api/                   # API路由
│       ├── __init__.py
│       ├── query.py           # 查询API
│       └── health.py          # 健康检查API
├── frontend/                  # 前端资源
│   ├── index.html             # 主页面
│   ├── components/            # Vue组件
│   │   ├── SearchForm.vue     # 搜索表单组件
│   │   └── StatCards.vue      # 统计卡片组件
│   └── assets/                # 静态资源
├── scripts/                   # 脚本工具
│   ├── start.sh               # 统一启动脚本
│   └── check_status.py        # 状态检查工具
├── tests/                     # 测试文件
├── logs/                      # 日志目录
├── .env                       # 环境变量
├── .env.example              # 环境变量模板
├── .gitignore                # Git忽略文件
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
└── run.py                    # 应用启动入口
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd database_query_system

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置你的参数
```

### 2. 启动方式

**完整启动（推荐）:**
```bash
./scripts/start.sh
```

**快速启动（开发环境）:**
```bash
./scripts/start.sh --quick
```

**清理启动（清理缓存后启动）:**
```bash
./scripts/start.sh --clean
```

**仅系统检查:**
```bash
./scripts/start.sh --check
```

**手动启动:**
```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python run.py
```

### 3. 访问系统

- **前端界面**: http://127.0.0.1:8081

## ⚙️ 配置说明

### 环境变量 (.env)

```bash
# Elasticsearch配置 - 线上环境
ES_HOST=es-cn-n8m2z2yo900054ak5.public.elasticsearch.aliyuncs.com
ES_PORT=9200
ES_USERNAME=elastic
ES_PASSWORD=elastic_n8m2z2yo
ES_INDEX=dc_tradecontrol
ES_TIMEOUT=15
ES_MAX_RETRIES=3
ES_REQUEST_TIMEOUT=15
ES_SCROLL_TIMEOUT=5m
ES_SEARCH_TIMEOUT=10s

# 应用配置
APP_HOST=127.0.0.1
APP_PORT=8081
DEBUG=True

# 缓存配置
CACHE_TTL=300
CACHE_SIZE=200

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
LOG_ERROR_BACKUP_COUNT=5
LOG_PERF_BACKUP_COUNT=3
LOG_ACCESS_MAX_BYTES=5242880
LOG_ACCESS_BACKUP_COUNT=3

# 性能监控配置
MONITOR_MAX_HISTORY=1000
MONITOR_REQUEST_HISTORY=100
MONITOR_CPU_WARNING_THRESHOLD=80
MONITOR_MEMORY_WARNING_THRESHOLD=85
MONITOR_ERROR_RATE_THRESHOLD=5
MONITOR_RESPONSE_TIME_WARNING=2000

# 健康检查配置
HEALTH_CHECK_TIMEOUT=5
```

### 配置说明

#### Elasticsearch配置
- `ES_HOST`: Elasticsearch服务器地址
- `ES_PORT`: Elasticsearch端口（默认9200）
- `ES_USERNAME/ES_PASSWORD`: 认证信息
- `ES_INDEX`: 索引名称
- `ES_TIMEOUT/ES_MAX_RETRIES`: 连接超时和重试配置
- `ES_REQUEST_TIMEOUT`: 请求超时时间（秒）
- `ES_SCROLL_TIMEOUT`: 滚动搜索超时时间
- `ES_SEARCH_TIMEOUT`: 搜索超时时间

#### 应用配置
- `APP_HOST/APP_PORT`: 应用服务器地址和端口
- `DEBUG`: 调试模式开关

#### 缓存配置
- `CACHE_TTL`: 缓存生存时间（秒）
- `CACHE_SIZE`: 缓存最大条目数

#### 日志配置
- `LOG_LEVEL`: 日志级别（DEBUG/INFO/WARNING/ERROR）
- `LOG_DIR`: 日志文件目录
- `LOG_MAX_BYTES`: 日志文件最大大小（字节）
- `LOG_BACKUP_COUNT`: 日志文件备份数量

#### 性能监控配置
- `MONITOR_MAX_HISTORY`: 性能历史记录最大数量
- `MONITOR_CPU_WARNING_THRESHOLD`: CPU使用率警告阈值（%）
- `MONITOR_MEMORY_WARNING_THRESHOLD`: 内存使用率警告阈值（%）
- `MONITOR_ERROR_RATE_THRESHOLD`: 错误率警告阈值（%）
- `MONITOR_RESPONSE_TIME_WARNING`: 响应时间警告阈值（毫秒）

---

**Database Query System v2.0** - 模块化架构 · 一键启动
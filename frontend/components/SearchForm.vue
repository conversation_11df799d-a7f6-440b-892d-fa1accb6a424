<template>
  <div class="search-form">
    <el-form :model="form" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="搜索关键词">
            <el-input
              v-model="form.searchKeyword"
              placeholder="请输入搜索关键词"
              @input="handleInputChange"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="ID搜索">
            <el-input
              v-model="form.idSearch"
              placeholder="请输入ID"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="内容来源">
            <el-select v-model="form.contentSource" placeholder="选择来源">
              <el-option label="全部" value="" />
              <el-option label="文件内容" value="files_v2" />
              <el-option label="文本内容" value="text" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="排序方式">
            <el-select v-model="form.orderBy">
              <el-option label="时间降序" value="public_time DESC" />
              <el-option label="时间升序" value="public_time ASC" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            搜索
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { debounce } from 'lodash-es'

const emit = defineEmits(['search'])

const loading = ref(false)
const form = reactive({
  searchKeyword: '',
  idSearch: '',
  contentSource: '',
  orderBy: 'public_time DESC'
})

// 防抖搜索
const handleInputChange = debounce(() => {
  if (form.searchKeyword.length > 2) {
    handleSearch()
  }
}, 300)

const handleSearch = () => {
  loading.value = true
  emit('search', { ...form })
}
</script>

<style scoped>
.search-form {
  background: rgba(248, 249, 250, 0.6);
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 30px;
  border: 1px solid rgba(233, 236, 239, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-form:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}
</style>
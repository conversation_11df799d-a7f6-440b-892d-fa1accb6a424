<template>
  <div class="table-container">
    <el-table 
      :data="data" 
      style="width: 100%"
      :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
      stripe>
      
      <el-table-column prop="id" label="ID" width="80" align="center">
        <template #default="scope">
          <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="title" label="标题" min-width="300">
        <template #default="scope">
          <div class="title-cell" v-html="scope.row.title_highlighted || scope.row.title"></div>
        </template>
      </el-table-column>

      <el-table-column prop="content" label="内容预览" min-width="400">
        <template #default="scope">
          <div class="content-preview"
               :title="scope.row.dynamic_content || scope.row.content"
               v-html="scope.row.dynamic_content_highlighted || scope.row.content_highlighted || scope.row.dynamic_content || scope.row.content">
            <div v-if="scope.row.content_source" class="content-source-tag">
              {{ scope.row.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="keyword" label="前端展示" width="120">
        <template #default="scope">
          <span class="keyword-tag">{{ scope.row.keywordShow }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="MonitorDate" label="发布日期" width="120">
        <template #default="scope">
          <span class="date-badge">{{ scope.row.MonitorDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="content_source" label="内容来源" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.content_source"
                  size="small"
                  :type="scope.row.content_source === 'files_v2' ? 'primary' : 'info'">
            {{ scope.row.content_source === 'files_v2' ? 'files_v2' : 'text' }}
          </el-tag>
          <span v-else style="color: #bdc3c7;">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="创建时间" width="125">
        <template #default="scope">
          <el-icon><Clock /></el-icon>
          {{ scope.row.createdAt }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="$emit('view-record', scope.row)"
            style="margin-right: 5px;">
            <el-icon><View /></el-icon>
            查看详情
          </el-button>
          <el-button
            v-if="scope.row.url"
            type="success"
            size="small"
            @click="$emit('open-url', scope.row.url)"
            link>
            <el-icon><Link /></el-icon>
            原文
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

defineProps({
  data: {
    type: Array,
    required: true
  }
})

defineEmits(['view-record', 'open-url'])
</script>

<style scoped>
.title-cell {
  font-weight: 500;
  color: #2c3e50;
}
</style>
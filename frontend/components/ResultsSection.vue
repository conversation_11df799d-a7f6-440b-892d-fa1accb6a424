<template>
  <div class="loading-container" v-if="loading">
    <el-icon class="is-loading" size="50" color="#667eea"><Loading /></el-icon>
    <p>正在查询数据...</p>
  </div>
  
  <div v-else-if="showNoData" class="no-data">
    <el-icon size="80" color="#bdc3c7"><DocumentRemove /></el-icon>
    <h3>未找到匹配的数据</h3>
    <p>请尝试调整搜索条件或关键词</p>
  </div>
  
  <div v-else-if="isGroupedView && hasGroupedData" class="grouped-results">
    <GroupedResults 
      :grouped-results="groupedResults"
      :group-expanded-state="groupExpandedState"
      :source-expanded-state="sourceExpandedState"
      @toggle-group="$emit('toggle-group', $event)"
      @toggle-source="$emit('toggle-source', $event)"
      @view-record="$emit('view-record', $event)"
      @open-url="$emit('open-url', $event)"
    />
    
    <!-- 分组视图分页 -->
    <div class="pagination-container" v-if="pagination.total_count > 0 && pageSize !== 'all'">
      <el-pagination
        v-model:current-page="pagination.current_page"
        :current-page="pagination.current_page"
        :page-size="pagination.page_size === 'all' ? 100 : pagination.page_size"
        :page-sizes="[10, 25, 50, 100]"
        :total="pagination.total_count"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
        background>
      </el-pagination>
    </div>

    <!-- 分组模式数据提示 -->
    <div class="pagination-container" v-if="hasGroupedData">
      <div class="data-info">
        <i class="el-icon-info"></i>
        分组模式：已展示全部数据，共 {{ totalRecords }} 条记录
        <span v-if="totalRecords >= 5000" class="performance-warning">
          (为保证性能，最多显示前5000条)
        </span>
      </div>
    </div>
  </div>

  <div v-else-if="queryResults.length > 0" class="table-results">
    <TableResults
      :data="queryResults"
      @view-record="$emit('view-record', $event)"
      @open-url="$emit('open-url', $event)"
    />
    
    <!-- 传统列表分页 -->
    <div class="pagination-container" v-if="pagination.total_count > 0 && pageSize !== 'all'">
      <el-pagination
        v-model:current-page="pagination.current_page"
        :current-page="pagination.current_page"
        :page-size="pagination.page_size === 'all' ? 100 : pagination.page_size"
        :page-sizes="[10, 25, 50, 100]"
        :total="pagination.total_count"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
        background>
      </el-pagination>
    </div>

    <!-- 全部数据提示 -->
    <div class="pagination-container" v-if="pageSize === 'all' && queryResults.length > 0">
      <div class="data-info">
        <i class="el-icon-info"></i>
        显示全部数据：共 {{ totalRecords }} 条记录
        <span v-if="totalRecords >= 5000" class="performance-warning">
          (为保证性能，最多显示前5000条)
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'
import GroupedResults from './GroupedResults.vue'
import TableResults from './TableResults.vue'

const props = defineProps({
  loading: Boolean,
  hasSearched: Boolean,
  queryResults: Array,
  groupedResults: Object,
  totalRecords: Number,
  isGroupedView: Boolean,
  groupExpandedState: Object,
  sourceExpandedState: Object,
  pagination: Object,
  pageSize: [String, Number]
})

defineEmits([
  'toggle-group',
  'toggle-source', 
  'view-record',
  'open-url',
  'size-change',
  'current-change'
])

const showNoData = computed(() => {
  return (!props.queryResults.length && !props.isGroupedView && props.hasSearched) || 
         (props.isGroupedView && !Object.keys(props.groupedResults).length && props.hasSearched)
})

const hasGroupedData = computed(() => {
  return Object.keys(props.groupedResults).length > 0
})
</script>

<style scoped>
.data-info {
  color: #666;
  font-size: 14px;
  text-align: center;
}

.performance-warning {
  color: #e6a23c;
  margin-left: 10px;
}
</style>
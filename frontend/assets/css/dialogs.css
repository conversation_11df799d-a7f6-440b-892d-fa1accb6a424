/* 详情对话框样式 */
.detail-container {
    max-height: 70vh;
    overflow-y: auto;
}

.es-record-detail {
    padding: 20px;
}

.record-summary {
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.summary-header h3 {
    margin: 0 0 15px 0;
    font-size: 1.5em;
    line-height: 1.3;
}

.summary-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.main-content-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.main-content-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.2em;
    font-weight: 600;
}

.main-content {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.content-display {
    line-height: 1.6;
    color: #212529;
    word-break: break-word;
}

.no-content {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.full-data-section {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.section-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
    font-weight: 600;
}

.formatted-data {
    padding: 20px;
}

.data-grid {
    display: grid;
    gap: 15px;
}

.data-item {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 15px;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #fafafa;
}

.data-item.highlight-field {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.field-name {
    font-weight: 600;
    color: #495057;
    word-break: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.field-value {
    color: #212529;
    word-break: break-word;
}

.array-value {
    background: #e3f2fd;
    padding: 8px;
    border-radius: 4px;
}

.array-item {
    padding: 4px 0;
    border-bottom: 1px solid #bbdefb;
}

.array-item:last-child {
    border-bottom: none;
}

.long-content {
    background: #f1f3f4;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #4285f4;
}

.content-preview {
    margin-bottom: 8px;
    line-height: 1.4;
}

.null-value, .empty-value {
    font-style: italic;
}

.url-value a {
    color: #1976d2;
    text-decoration: none;
    word-break: break-all;
}

.url-value a:hover {
    text-decoration: underline;
}

.raw-data {
    padding: 20px;
    background: #f8f9fa;
}

.json-display {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
}

.full-content-container {
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dialog-footer {
    text-align: right;
}

/* 对话框样式优化 */
.el-dialog {
    max-height: 90vh;
    overflow: hidden;
    margin-top: 5vh !important;
}

.el-dialog__body {
    max-height: calc(90vh - 120px);
    overflow-y: auto;
    padding: 20px;
}

/* 确保对话框在视口中居中显示 */
@media (min-height: 600px) {
    .el-dialog {
        margin-top: 8vh !important;
    }
}
/* 分页样式 */
.pagination-container {
    padding: 25px;
    text-align: center;
    background: rgba(248, 249, 250, 0.8);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(233, 236, 239, 0.5);
    border-radius: 0 0 16px 16px;
}

/* 来源分组样式 */
.source-section {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.source-header {
    background: #f1f3f4;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid #e9ecef;
}

.source-header:hover {
    background: #e8eaed;
}

.source-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.source-count {
    color: #666;
    font-weight: normal;
    font-size: 0.9em;
}

.source-toggle-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 0.9em;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.source-toggle-btn.collapsed {
    transform: rotate(-90deg);
}

.source-content {
    background: white;
}

.source-summary {
    display: flex;
    gap: 8px;
}

.source-tag {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75em;
    font-weight: 500;
}

.source-tag.files-v2 {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.source-tag.text {
    background: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #ce93d8;
}
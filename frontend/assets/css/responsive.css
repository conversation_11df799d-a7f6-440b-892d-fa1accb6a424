/* 响应式设计 */
@media (min-width: 1400px) {
    .container {
        max-width: 98%;
    }

    .content-preview {
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        max-width: 98%;
        padding: 15px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-item {
        min-width: unset;
    }

    .content-preview {
        max-width: 200px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .query-form {
        padding: 20px;
    }
    
    .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 1.8em;
    }
    
    .stat-number {
        font-size: 1.5em;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
}
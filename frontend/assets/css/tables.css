/* 表格相关样式 */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.el-table {
    border-radius: 16px;
    width: 100% !important;
    overflow: hidden;
}

.el-table .el-table__body-wrapper {
    overflow-x: auto;
}

.el-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #495057 !important;
    font-weight: 600;
    border: none !important;
    font-size: 0.9em;
    letter-spacing: 0.02em;
}

.el-table td {
    border-bottom: 1px solid #f1f3f4;
    padding: 16px 12px;
}

.el-table tr:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

.content-preview {
    max-width: 550px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
}

.content-source-tag {
    position: absolute;
    top: -8px;
    right: 0;
    background: #e8f4fd;
    color: #1976d2;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 0.7em;
    font-weight: 500;
    border: 1px solid #bbdefb;
    z-index: 1;
}

.content-source-indicator {
    margin-top: 2px;
}
/* 表单相关样式 */
.query-form {
    background: rgba(248, 249, 250, 0.6);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    border: 1px solid rgba(233, 236, 239, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.query-form:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.form-item {
    flex: 1;
    min-width: 200px;
}

/* 查询按钮行的特殊样式 */
.form-row:last-child {
    margin-top: 10px;
    padding-top: 15px;
    border-top: 1px solid rgba(233, 236, 239, 0.5);
}
/* Element Plus 组件自定义样式 */
.el-input__wrapper {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    transition: all 0.3s ease !important;
}

.el-input__wrapper:hover {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

.el-input__wrapper.is-focus {
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25) !important;
}

.el-select .el-input .el-input__wrapper {
    border-radius: 12px !important;
}

.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-button--primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

.el-button--primary:active {
    transform: translateY(0) !important;
}

.el-switch.is-checked .el-switch__core {
    background-color: #667eea !important;
}

.el-tag {
    border-radius: 20px !important;
    padding: 4px 12px !important;
    font-weight: 500 !important;
}

.el-pagination {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 8px !important;
}

.el-pagination .el-pager li {
    border-radius: 8px !important;
    margin: 0 2px !important;
}

.el-pagination .el-pager li.is-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}
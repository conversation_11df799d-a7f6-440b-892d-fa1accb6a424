/* 加载和空状态样式 */
.loading-container {
    text-align: center;
    padding: 80px 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    margin: 20px 0;
}

.loading-container p {
    margin-top: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.2em;
    font-weight: 600;
}

.no-data {
    text-align: center;
    padding: 80px 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    margin: 20px 0;
}

.no-data h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 25px 0 15px 0;
}

.no-data p {
    color: #6c7293;
    font-size: 1.1em;
}
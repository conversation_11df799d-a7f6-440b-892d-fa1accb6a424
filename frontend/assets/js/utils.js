// 应用配置
const APP_CONFIG = {
    API_BASE_URL: 'http://127.0.0.1:8081/api',
    DEFAULT_TABLE_NAME: 'dc_tradecontrol',
    DEFAULT_PAGE_SIZE: 50,
    MAX_CONTENT_LENGTH: 200
};

// API 请求工具类
class ApiService {
    static async query(params) {
        const response = await axios.post(`${APP_CONFIG.API_BASE_URL}/query`, params);
        return response.data;
    }
    
    static async getRecord(id, tableName) {
        const response = await axios.get(`${APP_CONFIG.API_BASE_URL}/record/${id}?table=${tableName}`);
        return response.data;
    }
}

// 工具函数
const Utils = {
    formatExecutionTime(time) {
        return time > 0 ? `${time}ms` : '-';
    },
    
    formatDate(dateString) {
        if (!dateString) return '-';
        return dateString;
    },
    
    isLongContent(value) {
        return typeof value === 'string' && value.length > APP_CONFIG.MAX_CONTENT_LENGTH;
    },
    
    isImportantField(key) {
        const importantFields = [
            'id', 'title', 'content', 'text', 'dynamic_content', 
            'dynamic_content_highlighted', 'keyword', 'public_time', 
            'url', 'content_source', '_score', 'isAddToQueue'
        ];
        return importantFields.includes(key);
    },
    
    getMainContent(record) {
        if (record.dynamic_content_highlighted) {
            return record.dynamic_content_highlighted;
        }
        if (record.dynamic_content) {
            return record.dynamic_content.replace(/\n/g, '<br>');
        }
        if (record.content) {
            return record.content.replace(/\n/g, '<br>');
        }
        if (record.text) {
            return record.text.replace(/\n/g, '<br>');
        }
        if (record.files_v2) {
            return Array.isArray(record.files_v2) ? record.files_v2.join('<br>') : record.files_v2;
        }
        return null;
    },
    
    getSourceTagType(source) {
        const types = {
            'files_v2': 'primary',
            'text': 'success',
            'unknown': 'info'
        };
        return types[source] || 'info';
    },
    
    getSourceLabel(source) {
        const labels = {
            'files_v2': '文件内容',
            'text': '文本内容',
            'unknown': '未知来源'
        };
        return labels[source] || source;
    },
    
    getContentSourceLabel(value) {
        const labels = {
            'files_v2': '文件内容',
            'text': '文本内容'
        };
        return labels[value] || value;
    }
};
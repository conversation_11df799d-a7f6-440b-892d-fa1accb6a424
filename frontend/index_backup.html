<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库查询系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            z-index: -1;
        }
        
        .container {
            max-width: 95%;
            width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }
        
        .header h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5em;
            margin-bottom: 15px;
            font-weight: 700;
            letter-spacing: -0.02em;
        }
        
        .header p {
            color: #6c7293;
            font-size: 1.2em;
            font-weight: 400;
            opacity: 0.8;
        }
        
        .query-form {
            background: rgba(248, 249, 250, 0.6);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 16px;
            margin-bottom: 30px;
            border: 1px solid rgba(233, 236, 239, 0.5);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .query-form:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: end;
        }
        
        .form-item {
            flex: 1;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            color: #2c3e50;
            padding: 25px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #6c7293;
            font-weight: 500;
            letter-spacing: 0.02em;
            text-transform: uppercase;
        }
        
        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .loading-container {
            text-align: center;
            padding: 80px 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            margin: 20px 0;
        }
        
        .loading-container p {
            margin-top: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .no-data {
            text-align: center;
            padding: 80px 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            margin: 20px 0;
        }
        
        .no-data h3 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 25px 0 15px 0;
        }
        
        .no-data p {
            color: #6c7293;
            font-size: 1.1em;
        }
        
        .el-table {
            border-radius: 16px;
            width: 100% !important;
            overflow: hidden;
        }

        .el-table .el-table__body-wrapper {
            overflow-x: auto;
        }
        
        .el-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            color: #495057 !important;
            font-weight: 600;
            border: none !important;
            font-size: 0.9em;
            letter-spacing: 0.02em;
        }
        
        .el-table td {
            border-bottom: 1px solid #f1f3f4;
            padding: 16px 12px;
        }
        
        .el-table tr:hover {
            background: rgba(102, 126, 234, 0.05) !important;
        }
        
        .content-preview {
            max-width: 500px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;
        }

        .content-source-tag {
            position: absolute;
            top: -8px;
            right: 0;
            background: #e8f4fd;
            color: #1976d2;
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 0.7em;
            font-weight: 500;
            border: 1px solid #bbdefb;
            z-index: 1;
        }

        .content-source-indicator {
            margin-top: 2px;
        }

        .pagination-container {
            padding: 25px;
            text-align: center;
            background: rgba(248, 249, 250, 0.8);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(233, 236, 239, 0.5);
            border-radius: 0 0 16px 16px;
        }

        /* 来源分组样式 */
        .source-section {
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .source-header {
            background: #f1f3f4;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            border-bottom: 1px solid #e9ecef;
        }

        .source-header:hover {
            background: #e8eaed;
        }

        .source-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .source-count {
            color: #666;
            font-weight: normal;
            font-size: 0.9em;
        }

        .source-toggle-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 0.9em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .source-toggle-btn.collapsed {
            transform: rotate(-90deg);
        }

        .source-content {
            background: white;
        }

        .source-summary {
            display: flex;
            gap: 8px;
        }

        .source-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75em;
            font-weight: 500;
        }

        .source-tag.files-v2 {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }

        .source-tag.text {
            background: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }
        
        .keyword-tag {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
            border: 1px solid rgba(21, 101, 192, 0.2);
            font-weight: 500;
            letter-spacing: 0.02em;
        }
        
        .date-badge {
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            color: #424242;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            border: 1px solid rgba(66, 66, 66, 0.1);
            font-weight: 500;
        }
        
        .url-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }

        /* 简化的详情展示样式 */
        .detail-container {
            max-height: 70vh;
            overflow-y: auto;
        }



        /* ES记录详情展示样式 */
        .es-record-detail {
            padding: 20px;
        }

        .record-summary {
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }

        .summary-header h3 {
            margin: 0 0 15px 0;
            font-size: 1.5em;
            line-height: 1.3;
        }

        .summary-meta {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .main-content-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .main-content-section h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.2em;
            font-weight: 600;
        }

        .main-content {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .content-display {
            line-height: 1.6;
            color: #212529;
            word-break: break-word;
        }

        .no-content {
            color: #6c757d;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .full-data-section {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h4 {
            margin: 0;
            color: #495057;
            font-size: 1.1em;
            font-weight: 600;
        }

        .formatted-data {
            padding: 20px;
        }

        .data-grid {
            display: grid;
            gap: 15px;
        }

        .data-item {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 15px;
            padding: 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: #fafafa;
        }

        .data-item.highlight-field {
            background: #fff3cd;
            border-color: #ffeaa7;
        }

        .field-name {
            font-weight: 600;
            color: #495057;
            word-break: break-word;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .field-value {
            color: #212529;
            word-break: break-word;
        }

        .array-value {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
        }

        .array-item {
            padding: 4px 0;
            border-bottom: 1px solid #bbdefb;
        }

        .array-item:last-child {
            border-bottom: none;
        }

        .long-content {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #4285f4;
        }

        .content-preview {
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .null-value, .empty-value {
            font-style: italic;
        }

        .url-value a {
            color: #1976d2;
            text-decoration: none;
            word-break: break-all;
        }

        .url-value a:hover {
            text-decoration: underline;
        }

        .raw-data {
            padding: 20px;
            background: #f8f9fa;
        }

        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .full-content-container {
            max-height: 60vh;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .dialog-footer {
            text-align: right;
        }

        /* 关键词组合分组样式 */
        .group-container {
            margin-bottom: 30px;
        }

        .group-header {
            background: #f8f9fa;
            color: #2c3e50;
            padding: 12px 16px;
            border-radius: 6px 6px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border: 1px solid #e9ecef;
            border-bottom: none;
        }

        .group-header:hover {
            background: #e9ecef;
        }

        .group-title {
            font-size: 1em;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .group-count {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .group-content {
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 6px 6px;
            overflow: hidden;
        }

        .keyword-combination {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
        }

        .combination-tag {
            background: #667eea;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .group-toggle-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .group-toggle-btn.collapsed {
            transform: rotate(-90deg);
        }

        .view-mode-switch {
            margin-bottom: 20px;
            text-align: center;
        }

        .mode-switch-btn {
            margin: 0 10px;
            padding: 8px 20px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mode-switch-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .group-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .group-stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .group-stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .group-stat-label {
            font-size: 0.85em;
            color: #7f8c8d;
        }

        /* 响应式设计 */
        @media (min-width: 1400px) {
            .container {
                max-width: 98%;
            }

            .content-preview {
                max-width: 600px;
            }
        }

        @media (max-width: 768px) {
            .container {
                max-width: 98%;
                padding: 15px;
            }

            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .content-preview {
                max-width: 200px;
            }
        }
        
        /* Element Plus 组件自定义样式 */
        .el-input__wrapper {
            border-radius: 12px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
            transition: all 0.3s ease !important;
        }
        
        .el-input__wrapper:hover {
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
        }
        
        .el-input__wrapper.is-focus {
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25) !important;
        }
        
        .el-select .el-input .el-input__wrapper {
            border-radius: 12px !important;
        }
        
        .el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 12px !important;
            padding: 12px 24px !important;
            font-weight: 600 !important;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }
        
        .el-button--primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
        }
        
        .el-button--primary:active {
            transform: translateY(0) !important;
        }
        
        .el-switch.is-checked .el-switch__core {
            background-color: #667eea !important;
        }
        
        .el-tag {
            border-radius: 20px !important;
            padding: 4px 12px !important;
            font-weight: 500 !important;
        }
        
        .el-pagination {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 8px !important;
        }
        
        .el-pagination .el-pager li {
            border-radius: 8px !important;
            margin: 0 2px !important;
        }
        
        .el-pagination .el-pager li.is-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>🔍 数据库查询系统</h1>
                <p>智能化参数查询，美观数据展示</p>
            </div>
            
            <!-- 查询表单 -->
            <div class="query-form">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">
                    <el-icon><Search /></el-icon>
                    查询参数设置
                </h3>
                
                <div class="form-row">
                    <div class="form-item">
                        <el-form-item label="搜索关键词">
                            <el-input
                                v-model="queryParams.searchKeyword"
                                placeholder="请输入搜索关键词，多个关键词用逗号分隔"
                                clearable
                                prefix-icon="Search">
                                <template #append>
                                    <el-tooltip content="支持多关键词搜索，用逗号分隔，如：美国,特朗普" placement="top">
                                        <el-icon><QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </template>
                            </el-input>
                        </el-form-item>
                    </div>

                    <div class="form-item">
                        <el-form-item label="ID搜索">
                            <el-input
                                v-model="queryParams.idSearch"
                                placeholder="请输入ID，多个ID用逗号分隔"
                                clearable
                                prefix-icon="Key">
                                <template #append>
                                    <el-tooltip content="支持多ID搜索，用逗号分隔，如：123,456,789" placement="top">
                                        <el-icon><QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </template>
                            </el-input>
                        </el-form-item>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-item">
                        <el-form-item label="内容来源">
                            <el-select v-model="queryParams.contentSourceFilter" placeholder="选择内容来源" clearable>
                                <el-option label="全部来源" :value="null"></el-option>
                                <el-option label="文件内容" value="files_v2"></el-option>
                                <el-option label="文本内容" value="text"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    

                    
                    <div class="form-item" style="flex: 0.5;">
                        <el-form-item label="分组显示">
                            <el-switch
                                v-model="queryParams.enableGrouping"
                                active-text="按关键词分组"
                                inactive-text="传统列表"
                                :active-value="true"
                                :inactive-value="false"
                                style="--el-switch-on-color: #667eea; --el-switch-off-color: #dcdfe6"
                                @change="handleGroupingChange">
                            </el-switch>
                            <div v-if="queryParams.enableGrouping && isGroupedView" style="margin-top: 5px; font-size: 12px; color: #666;">
                                <i class="el-icon-info"></i> 分组模式将自动展示全部数据
                            </div>
                        </el-form-item>
                    </div>

                </div>
                
                <div class="form-row">
                    <div class="form-item">
                        <el-form-item label="排序字段">
                            <el-select v-model="queryParams.orderBy" placeholder="选择排序方式">
                                <el-option label="发布时间 (降序)" value="public_time DESC"></el-option>
                                <el-option label="发布时间 (升序)" value="public_time ASC"></el-option>
                                <el-option label="相关性评分 (降序)" value="_score DESC"></el-option>
                                <el-option label="相关性评分 (升序)" value="_score ASC"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>

                    <div class="form-item">
                        <el-form-item label="每页显示">
                            <el-select v-model="queryParams.pageSize" placeholder="选择每页显示数量" @change="handlePageSizeChange">
                                <el-option label="10条" :value="10"></el-option>
                                <el-option label="25条" :value="25"></el-option>
                                <el-option label="50条" :value="50"></el-option>
                                <el-option label="100条" :value="100"></el-option>
                                <el-option label="全部数据" :value="all"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    
                    <div class="form-item">
                        <el-form-item label="ES索引">
                            <el-input
                                v-model="queryParams.tableName"
                                placeholder="Elasticsearch索引名称"
                                readonly>
                            </el-input>
                        </el-form-item>
                    </div>
                    
                    <div class="form-item" style="flex: 0.3;">
                        <el-button 
                            type="primary" 
                            @click="executeQuery" 
                            :loading="loading"
                            size="large"
                            style="width: 100%; background: linear-gradient(45deg, #667eea, #764ba2); border: none;">
                            <el-icon><Search /></el-icon>
                            {{ loading ? '查询中...' : '执行查询' }}
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div v-if="(queryResults.length > 0 && !isGroupedView) || (isGroupedView && Object.keys(groupedResults).length > 0)" class="stats-cards">
                <div class="stat-card" v-if="!isGroupedView">
                    <div class="stat-number">{{ queryResults.length }}</div>
                    <div class="stat-label">查询结果</div>
                </div>
                <div class="stat-card" v-if="isGroupedView">
                    <div class="stat-number">{{ totalRecords }}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card" v-if="isGroupedView">
                    <div class="stat-number">{{ Object.keys(groupedResults).length }}</div>
                    <div class="stat-label">关键词组合</div>
                </div>
                <div class="stat-card" v-if="queryParams.searchKeyword">
                    <div class="stat-number">{{ queryParams.searchKeyword }}</div>
                    <div class="stat-label">搜索关键词</div>
                </div>

                <div class="stat-card" v-if="queryParams.idSearch">
                    <div class="stat-number">{{ queryParams.idSearch }}</div>
                    <div class="stat-label">ID搜索</div>
                </div>

                <div class="stat-card" v-if="queryParams.contentSourceFilter">
                    <div class="stat-number">{{ getContentSourceLabel(queryParams.contentSourceFilter) }}</div>
                    <div class="stat-label">内容来源</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">{{ formatExecutionTime }}</div>
                    <div class="stat-label">执行时间</div>
                </div>
            </div>
            
            <!-- 查询结果 -->
            <div class="results-section">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-container">
                    <el-icon class="is-loading" size="50" color="#667eea"><Loading /></el-icon>
                    <p style="margin-top: 20px; color: #667eea; font-size: 1.1em;">正在查询数据...</p>
                </div>
                
                <!-- 无数据状态 -->
                <div v-else-if="(!queryResults.length && !isGroupedView && hasSearched) || (isGroupedView && !Object.keys(groupedResults).length && hasSearched)" class="no-data">
                    <el-icon size="80" color="#bdc3c7"><DocumentRemove /></el-icon>
                    <h3 style="color: #7f8c8d; margin: 20px 0;">未找到匹配的数据</h3>
                    <p style="color: #95a5a6;">请尝试调整搜索条件或关键词</p>
                </div>
                
                <!-- 分组视图 -->
                <div v-else-if="isGroupedView && Object.keys(groupedResults).length > 0">
                    <div v-for="(group, groupKey) in groupedResults" :key="groupKey" class="group-container">
                        <div class="group-header" @click="toggleGroup(groupKey)">
                            <div class="group-title">
                                <span>🔍</span>
                                <div class="keyword-combination">
                                    <span v-for="keyword in group.keywords" :key="keyword" class="combination-tag">
                                        {{ keyword }}
                                    </span>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div class="group-count">{{ group.total_count || 0 }} 条记录</div>
                                <div class="source-summary">
                                    <span v-if="group.sources && group.sources.files_v2 && group.sources.files_v2.count > 0" class="source-tag files-v2">
                                        文件: {{ group.sources.files_v2.count }}
                                    </span>
                                    <span v-if="group.sources && group.sources.text && group.sources.text.count > 0" class="source-tag text">
                                        文本: {{ group.sources.text.count }}
                                    </span>
                                </div>
                                <button class="group-toggle-btn" :class="{ collapsed: !groupExpandedState[groupKey] }">
                                    ▼
                                </button>
                            </div>
                        </div>
                        
                        <div v-show="groupExpandedState[groupKey]" class="group-content">
                            <!-- 按来源分别显示 -->
                            <template v-for="(sourceData, sourceKey) in (group.sources || {})" :key="sourceKey">
                                <div v-if="sourceData && sourceData.count > 0" class="source-section">
                                <div class="source-header">
                                    <div class="source-title">
                                        <span v-if="sourceKey === 'files_v2'">📄</span>
                                        <span v-else>📝</span>
                                        {{ sourceData.source_name || sourceKey }}
                                        <span class="source-count">({{ sourceData.count || 0 }} 条)</span>
                                    </div>
                                    <button class="source-toggle-btn" @click="toggleSource(groupKey, sourceKey)" :class="{ collapsed: !getSourceExpandedState(groupKey, sourceKey) }">
                                        ▼
                                    </button>
                                </div>

                                <div v-show="getSourceExpandedState(groupKey, sourceKey)" class="source-content">
                                    <el-table
                                        :data="sourceData.records || []"
                                        style="width: 100%"
                                        :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
                                        stripe>
                                
                                <el-table-column prop="id" label="ID" width="80" align="center">
                                    <template #default="scope">
                                        <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="title" label="标题" min-width="300">
                                    <template #default="scope">
                                        <div style="font-weight: 500; color: #2c3e50;" v-html="scope.row.title_highlighted || scope.row.title">
                                        </div>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="content" label="内容预览" min-width="400">
                                    <template #default="scope">
                                        <div class="content-preview"
                                             :title="scope.row.dynamic_content || scope.row.content"
                                             v-html="scope.row.dynamic_content_highlighted || scope.row.content_highlighted || scope.row.dynamic_content || scope.row.content">
                                            <div v-if="scope.row.content_source" class="content-source-tag">
                                                {{ scope.row.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="keyword" label="前端展示" width="120">
                                    <template #default="scope">
                                        <span class="keyword-tag">{{ scope.row.keywordShow }}</span>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="MonitorDate" label="发布日期" width="120">
                                    <template #default="scope">
                                        <span class="date-badge">{{ scope.row.MonitorDate }}</span>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="content_source" label="内容来源" width="100">
                                    <template #default="scope">
                                        <el-tag v-if="scope.row.content_source"
                                                size="small"
                                                :type="scope.row.content_source === 'files_v2' ? 'primary' : 'info'">
                                            {{ scope.row.content_source === 'files_v2' ? 'files_v2' : 'text' }}
                                        </el-tag>
                                        <span v-else style="color: #bdc3c7;">-</span>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="createdAt" label="创建时间" width="120">
                                    <template #default="scope">
                                        <el-icon><Clock /></el-icon>
                                        {{ scope.row.createdAt }}
                                    </template>
                                </el-table-column>

                                <el-table-column label="匹配关键词" width="150">
                                    <template #default="scope">
                                        <div class="keyword-combination">
                                            <span v-for="keyword in scope.row.matched_keywords" :key="keyword" class="combination-tag" style="font-size: 0.75em;">
                                                {{ keyword }}
                                            </span>
                                        </div>
                                    </template>
                                </el-table-column>

                                <el-table-column label="操作" width="150" align="center">
                                    <template #default="scope">
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="viewRecord(scope.row)"
                                            style="margin-right: 5px;">
                                            <el-icon><View /></el-icon>
                                            查看详情
                                        </el-button>
                                        <el-button
                                            v-if="scope.row.url"
                                            type="success"
                                            size="small"
                                            @click="openUrl(scope.row.url)"
                                            link>
                                            <el-icon><Link /></el-icon>
                                            原文
                                        </el-button>
                                    </template>
                                </el-table-column>
                                    </el-table>
                                </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 分组视图分页控件 -->
                    <div class="pagination-container" v-if="pagination.total_count > 0 && queryParams.pageSize !== 'all'">
                        <el-pagination
                            v-model:current-page="pagination.current_page"
                            :current-page="pagination.current_page"
                            :page-size="pagination.page_size === 'all' ? 100 : pagination.page_size"
                            :page-sizes="[10, 25, 50, 100]"
                            :total="pagination.total_count"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            background>
                        </el-pagination>
                    </div>

                    <!-- 分组模式全部数据提示 -->
                    <div class="pagination-container" v-if="Object.keys(groupedResults).length > 0">
                        <div style="color: #666; font-size: 14px; text-align: center;">
                            <i class="el-icon-info"></i>
                            分组模式：已展示全部数据，共 {{ totalRecords }} 条记录
                            <span v-if="totalRecords >= 5000" style="color: #e6a23c; margin-left: 10px;">
                                (为保证性能，最多显示前5000条)
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 传统列表视图 -->
                <div v-else-if="queryResults.length > 0" class="table-container">
                    <el-table 
                        :data="queryResults" 
                        style="width: 100%"
                        :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
                        stripe>
                        
                        <el-table-column prop="id" label="ID" width="80" align="center">
                            <template #default="scope">
                                <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="title" label="标题" min-width="300">
                            <template #default="scope">
                                <div style="font-weight: 500; color: #2c3e50;" v-html="scope.row.title_highlighted || scope.row.title">
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column prop="content" label="内容预览" min-width="400">
                            <template #default="scope">
                                <div class="content-preview"
                                     :title="scope.row.dynamic_content || scope.row.content"
                                     v-html="scope.row.dynamic_content_highlighted || scope.row.content_highlighted || scope.row.dynamic_content || scope.row.content">
                                    <div v-if="scope.row.content_source" class="content-source-tag">
                                        {{ scope.row.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="keyword" label="前端展示" width="120">
                            <template #default="scope">
                                <span class="keyword-tag">{{ scope.row.keywordShow }}</span>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="MonitorDate" label="发布日期" width="120">
                            <template #default="scope">
                                <span class="date-badge">{{ scope.row.MonitorDate }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="content_source" label="内容来源" width="100">
                            <template #default="scope">
                                <el-tag v-if="scope.row.content_source"
                                        size="small"
                                        :type="scope.row.content_source === 'files_v2' ? 'primary' : 'info'">
                                    {{ scope.row.content_source === 'files_v2' ? 'files_v2' : 'text' }}
                                </el-tag>
                                <span v-else style="color: #bdc3c7;">-</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="createdAt" label="创建时间" width="120">
                            <template #default="scope">
                                <el-icon><Clock /></el-icon>
                                {{ scope.row.createdAt }}
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="150" align="center">
                            <template #default="scope">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="viewRecord(scope.row)"
                                    style="margin-right: 5px;">
                                    <el-icon><View /></el-icon>
                                    查看详情
                                </el-button>
                                <el-button
                                    v-if="scope.row.url"
                                    type="success"
                                    size="small"
                                    @click="openUrl(scope.row.url)"
                                    link>
                                    <el-icon><Link /></el-icon>
                                    原文
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页控件 -->
                    <div class="pagination-container" v-if="pagination.total_count > 0 && queryParams.pageSize !== 'all'">
                        <el-pagination
                            v-model:current-page="pagination.current_page"
                            :current-page="pagination.current_page"
                            :page-size="pagination.page_size === 'all' ? 100 : pagination.page_size"
                            :page-sizes="[10, 25, 50, 100]"
                            :total="pagination.total_count"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            background>
                        </el-pagination>
                    </div>

                    <!-- 全部数据提示 -->
                    <div class="pagination-container" v-if="queryParams.pageSize === 'all' && queryResults.length > 0">
                        <div style="color: #666; font-size: 14px;">
                            <i class="el-icon-info"></i>
                            显示全部数据：共 {{ totalRecords }} 条记录
                            <span v-if="totalRecords >= 5000" style="color: #e6a23c; margin-left: 10px;">
                                (为保证性能，最多显示前5000条)
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详情查看对话框 -->
            <el-dialog
                v-model="detailDialogVisible"
                title="ES记录完整数据"
                width="90%"
                :before-close="handleDetailClose">

                <div v-if="currentRecord" class="detail-container">
                    <!-- ES完整数据展示 -->
                    <div class="es-record-detail">
                        <!-- 记录概要 -->
                        <div class="record-summary">
                            <div class="summary-header">
                                <h3>{{ currentRecord.title || '无标题' }}</h3>
                                <div class="summary-meta">
                                    <el-tag v-if="currentRecord.id" type="info" size="small">ID: {{ currentRecord.id }}</el-tag>
                                    <el-tag v-if="currentRecord.content_source" :type="getSourceTagType(currentRecord.content_source)" size="small">
                                        {{ getSourceLabel(currentRecord.content_source) }}
                                    </el-tag>
                                    <el-tag v-if="currentRecord.public_time" type="success" size="small">
                                        {{ formatDate(currentRecord.public_time) }}
                                    </el-tag>
                                    <el-tag v-if="currentRecord._score" type="warning" size="small">
                                        评分: {{ currentRecord._score.toFixed(2) }}
                                    </el-tag>
                                </div>
                            </div>
                        </div>

                        <!-- 主要内容展示 -->
                        <div class="main-content-section">
                            <h4><i class="el-icon-document"></i> 主要内容</h4>
                            <div class="main-content">
                                <!-- 优先显示高亮内容，然后是动态内容，最后是原始内容 -->
                                <div v-if="getMainContent(currentRecord)" class="content-display" v-html="getMainContent(currentRecord)"></div>
                                <div v-else class="no-content">暂无主要内容</div>
                            </div>
                        </div>

                        <!-- 完整ES数据展示 -->
                        <div class="full-data-section">
                            <div class="section-header">
                                <h4><i class="el-icon-data-line"></i> 完整ES数据</h4>
                                <el-button size="small" @click="toggleDataView" type="text">
                                    {{ showRawData ? '显示格式化' : '显示原始JSON' }}
                                </el-button>
                            </div>

                            <!-- 格式化数据展示 -->
                            <div v-if="!showRawData" class="formatted-data">
                                <div class="data-grid">
                                    <div v-for="(value, key) in currentRecord" :key="key" class="data-item" :class="{ 'highlight-field': isImportantField(key) }">
                                        <div class="field-name">{{ key }}</div>
                                        <div class="field-value">
                                            <!-- 处理不同类型的值 -->
                                            <div v-if="key === 'url' && value" class="url-value">
                                                <a :href="value" target="_blank" class="url-link">{{ value }}</a>
                                            </div>
                                            <div v-else-if="Array.isArray(value)" class="array-value">
                                                <el-tag v-if="value.length === 0" size="small" type="info">空数组</el-tag>
                                                <div v-else>
                                                    <div v-for="(item, index) in value" :key="index" class="array-item">
                                                        {{ item }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else-if="isLongContent(value)" class="long-content">
                                                <div class="content-preview">{{ String(value).substring(0, 200) }}...</div>
                                                <el-button size="small" type="text" @click="showFullContent(key, value)">查看完整内容</el-button>
                                            </div>
                                            <div v-else-if="value === null || value === undefined" class="null-value">
                                                <el-tag size="small" type="info">null</el-tag>
                                            </div>
                                            <div v-else-if="value === ''" class="empty-value">
                                                <el-tag size="small" type="info">空字符串</el-tag>
                                            </div>
                                            <div v-else class="normal-value">{{ value }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 原始JSON数据展示 -->
                            <div v-else class="raw-data">
                                <pre class="json-display">{{ JSON.stringify(currentRecord, null, 2) }}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="detailDialogVisible = false">关闭</el-button>
                        <el-button type="primary" @click="copyFullData">
                            <el-icon><CopyDocument /></el-icon>
                            复制完整数据
                        </el-button>
                        <el-button type="success" @click="copyMainContent">
                            <el-icon><Document /></el-icon>
                            复制主要内容
                        </el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 完整内容查看对话框 -->
            <el-dialog
                v-model="fullContentDialogVisible"
                :title="fullContentTitle"
                width="80%"
                :before-close="handleFullContentClose">

                <div class="full-content-container">
                    <div class="content-display" v-html="fullContentValue"></div>
                </div>

                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="fullContentDialogVisible = false">关闭</el-button>
                        <el-button type="primary" @click="copySpecificContent">
                            <el-icon><CopyDocument /></el-icon>
                            复制此内容
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>

    <script>
        const { createApp, reactive, ref } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    hasSearched: false,
                    executionTime: 0,
                    detailDialogVisible: false,
                    currentRecord: null,
                    showRawData: false,

                    // 完整内容对话框相关
                    fullContentDialogVisible: false,
                    fullContentTitle: '',
                    fullContentValue: '',
                    queryParams: {
                        searchKeyword: '',
                        idSearch: '',
                        searchFields: ['files_v2.file_v2_original', 'text'],
                        orderBy: 'public_time DESC',
                        tableName: 'dc_tradecontrol',
                        enableGrouping: true,
                        page: 1,
                        pageSize: 50,
                        contentSourceFilter: null
                    },
                    pagination: {
                        current_page: 1,
                        page_size: 50,
                        total_count: 0,
                        total_pages: 0,
                        has_next: false,
                        has_prev: false
                    },
                    queryResults: [],
                    groupedResults: {},
                    totalRecords: 0,
                    groupExpandedState: {},
                    sourceExpandedState: {},
                    // 模拟数据
                    mockData: [
                        {
                            id: 12,
                            companyId: 158,
                            customer_id: 22,
                            MonitorDate: '2025-07-20',
                            keywordShow: '贾茹',
                            keyword: '贾茹',
                            keywordRelation: 'aa',
                            newsId: '1',
                            title: '参议员布鲁门撒尔：一周回顾（4月18日至4月25日）',
                            content: '文章讨论了特朗普总统利用行政命令打击其认为的敌人，特别是针对不支持其代理律师托德·布兰奇的律所。这些命令被认为是对美国法治的威胁...',
                            newsSource: '参议院官网',
                            page: 3,
                            type: 2,
                            url: 'https://www.blumenthal.senate.gov/newsroom/press/release',
                            createdAt: '2025-04-26 06:27:39'
                        },
                        {
                            id: 13,
                            companyId: 158,
                            customer_id: 22,
                            MonitorDate: '2025-07-19',
                            keywordShow: '君正集成电路',
                            keyword: '君正集成电路',
                            keywordRelation: 'bb',
                            newsId: '2',
                            title: '参议员布鲁门撒尔：一周回顾（4月18日至4月25日）',
                            content: '文章主要讨论了特朗普总统发布的一系列行政命令，这些命令针对特定律师事务所，试图通过恐吓和限制其法律代表来削弱美国的司法独立性...',
                            newsSource: '政府网站',
                            page: 3,
                            type: 2,
                            url: 'https://www.blumenthal.senate.gov/newsroom/press/release',
                            createdAt: '2025-04-26 06:27:39'
                        }
                    ]
                }
            },
            computed: {
                formatExecutionTime() {
                    return this.executionTime > 0 ? `${this.executionTime}ms` : '-';
                },
                isGroupedView() {
                    return this.queryParams.enableGrouping && this.queryParams.searchKeyword.includes(',');
                }
            },
            methods: {
                async executeQuery() {
                    // 验证输入
                    if (!this.queryParams.searchKeyword.trim() && !this.queryParams.idSearch.trim()) {
                        ElMessage.warning('请输入搜索关键词或ID');
                        return;
                    }

                    this.loading = true;
                    this.hasSearched = true;

                    try {
                        // 调用后端API
                        const response = await axios.post('http://127.0.0.1:8081/api/query', {
                            searchKeyword: this.queryParams.searchKeyword,
                            idSearch: this.queryParams.idSearch,
                            searchFields: this.queryParams.searchFields,
                            orderBy: this.queryParams.orderBy,
                            tableName: this.queryParams.tableName,
                            enableGrouping: this.queryParams.enableGrouping,
                            page: this.queryParams.page,
                            pageSize: this.queryParams.pageSize,
                            contentSourceFilter: this.queryParams.contentSourceFilter
                        });

                        if (response.data.success) {
                            if (response.data.meta.grouped) {
                                // 分组数据
                                this.groupedResults = response.data.data || {};
                                this.totalRecords = response.data.meta.total_records || 0;
                                this.queryResults = [];

                                // 初始化分组展开状态
                                this.initializeExpandedStates();
                            } else {
                                // 传统列表数据
                                this.queryResults = response.data.data;
                                this.groupedResults = {};
                                this.totalRecords = response.data.meta.total;

                                // 更新分页信息
                                if (response.data.pagination) {
                                    this.pagination = response.data.pagination;
                                    this.queryParams.page = this.pagination.current_page;

                                    // 只有在非全部数据模式下才更新pageSize，避免被分页控件覆盖
                                    if (this.pagination.page_size !== 'all') {
                                        this.queryParams.pageSize = this.pagination.page_size;
                                    }
                                }
                            }

                            this.executionTime = response.data.meta.execution_time;
                            ElMessage.success(response.data.message);
                        } else {
                            ElMessage.error(response.data.message);
                            this.queryResults = [];
                            this.groupedResults = {};
                        }

                    } catch (error) {
                        console.error('查询失败:', error);

                        // 重置状态
                        this.queryResults = [];
                        this.groupedResults = {};
                        this.totalRecords = 0;
                        this.executionTime = 0;

                        // 如果API调用失败，使用模拟数据作为后备
                        if (error.code === 'ERR_NETWORK') {
                            ElMessage.warning('无法连接到服务器，使用模拟数据展示');
                            this.queryResults = this.mockData.filter(item =>
                                this.queryParams.searchFields.some(field =>
                                    item[field] && item[field].includes(this.queryParams.searchKeyword.split(',')[0])
                                )
                            );
                            this.executionTime = 150; // 模拟执行时间
                            this.totalRecords = this.queryResults.length;
                        } else {
                            ElMessage.error('查询失败，请检查网络连接或参数设置');
                            this.queryResults = [];
                            this.groupedResults = {};
                        }
                    } finally {
                        this.loading = false;
                    }
                },
                
                toggleGroup(groupKey) {
                    // 创建新的状态对象以确保响应式更新
                    const newState = { ...this.groupExpandedState };
                    newState[groupKey] = !newState[groupKey];
                    this.groupExpandedState = newState;
                },

                toggleSource(groupKey, sourceKey) {
                    try {
                        // 创建新的状态对象
                        const newState = { ...this.sourceExpandedState };
                        if (!newState[groupKey]) {
                            newState[groupKey] = {};
                        }
                        newState[groupKey] = { ...newState[groupKey] };
                        newState[groupKey][sourceKey] = !newState[groupKey][sourceKey];

                        this.sourceExpandedState = newState;
                    } catch (error) {
                        console.error('切换来源展开状态时出错:', error);
                    }
                },

                getSourceExpandedState(groupKey, sourceKey) {
                    try {
                        return this.sourceExpandedState &&
                               this.sourceExpandedState[groupKey] &&
                               this.sourceExpandedState[groupKey][sourceKey];
                    } catch (error) {
                        console.warn('获取来源展开状态时出错:', error);
                        return true; // 默认展开
                    }
                },
                
                formatDate(dateString) {
                    if (!dateString) return '-';
                    return dateString; // 现在后端已经格式化为年月日
                },

                openUrl(url) {
                    if (url) {
                        window.open(url, '_blank');
                    }
                },

                async viewRecord(row) {
                    try {
                        // 调用API获取完整记录信息
                        const response = await axios.get(`http://127.0.0.1:8081/api/record/${row.id}?table=${this.queryParams.tableName}`);

                        if (response.data.success) {
                            this.currentRecord = response.data.data;
                            this.detailDialogVisible = true;
                        } else {
                            ElMessage.error('获取记录详情失败');
                        }
                    } catch (error) {
                        console.error('获取记录详情失败:', error);
                        // 如果API调用失败，使用当前行数据
                        this.currentRecord = row;
                        this.detailDialogVisible = true;
                    }
                },

                handleDetailClose() {
                    this.detailDialogVisible = false;
                    this.currentRecord = null;
                },



                getMainContent(record) {
                    // 优先级：高亮内容 > 动态内容 > 原始内容 > 文件内容
                    if (record.dynamic_content_highlighted) {
                        return record.dynamic_content_highlighted;
                    }
                    if (record.dynamic_content) {
                        return record.dynamic_content.replace(/\n/g, '<br>');
                    }
                    if (record.content) {
                        return record.content.replace(/\n/g, '<br>');
                    }
                    if (record.text) {
                        return record.text.replace(/\n/g, '<br>');
                    }
                    if (record.files_v2) {
                        return Array.isArray(record.files_v2) ? record.files_v2.join('<br>') : record.files_v2;
                    }
                    return null;
                },

                isImportantField(key) {
                    const importantFields = ['id', 'title', 'content', 'text', 'dynamic_content', 'dynamic_content_highlighted', 'keyword', 'public_time', 'url', 'content_source', '_score', 'isAddToQueue'];
                    return importantFields.includes(key);
                },

                isLongContent(value) {
                    return typeof value === 'string' && value.length > 200;
                },

                toggleDataView() {
                    this.showRawData = !this.showRawData;
                },

                showFullContent(fieldName, content) {
                    this.fullContentTitle = `完整内容 - ${fieldName}`;
                    this.fullContentValue = typeof content === 'string' ? content.replace(/\n/g, '<br>') : JSON.stringify(content, null, 2);
                    this.fullContentDialogVisible = true;
                },

                handleFullContentClose() {
                    this.fullContentDialogVisible = false;
                    this.fullContentTitle = '';
                    this.fullContentValue = '';
                },

                copyFullData() {
                    if (!this.currentRecord) return;

                    const jsonData = JSON.stringify(this.currentRecord, null, 2);
                    navigator.clipboard.writeText(jsonData).then(() => {
                        ElMessage.success('完整ES数据已复制到剪贴板');
                    }).catch(() => {
                        ElMessage.error('复制失败，请手动复制');
                    });
                },

                copyMainContent() {
                    if (!this.currentRecord) return;

                    const mainContent = this.getMainContent(this.currentRecord);
                    if (mainContent) {
                        // 移除HTML标签
                        const textContent = mainContent.replace(/<[^>]*>/g, '');
                        navigator.clipboard.writeText(textContent).then(() => {
                            ElMessage.success('主要内容已复制到剪贴板');
                        }).catch(() => {
                            ElMessage.error('复制失败，请手动复制');
                        });
                    } else {
                        ElMessage.warning('没有找到主要内容');
                    }
                },

                copySpecificContent() {
                    if (this.fullContentValue) {
                        // 移除HTML标签
                        const textContent = this.fullContentValue.replace(/<[^>]*>/g, '');
                        navigator.clipboard.writeText(textContent).then(() => {
                            ElMessage.success('内容已复制到剪贴板');
                        }).catch(() => {
                            ElMessage.error('复制失败，请手动复制');
                        });
                    }
                },

                getSourceTagType(source) {
                    const types = {
                        'files_v2': 'primary',
                        'text': 'success',
                        'unknown': 'info'
                    };
                    return types[source] || 'info';
                },

                getSourceLabel(source) {
                    const labels = {
                        'files_v2': '文件内容',
                        'text': '文本内容',
                        'unknown': '未知来源'
                    };
                    return labels[source] || source;
                },

                // 分页相关方法
                handleCurrentChange(page) {
                    this.queryParams.page = page;
                    this.executeQuery();
                },

                handleSizeChange(size) {
                    // 如果当前是全部数据模式，忽略分页控件的变更
                    if (this.queryParams.pageSize === 'all') {
                        return;
                    }

                    this.queryParams.pageSize = size;
                    this.queryParams.page = 1; // 重置到第一页
                    this.executeQuery();
                },

                handlePageSizeChange() {
                    this.queryParams.page = 1; // 重置到第一页

                    // 如果选择全部数据，给用户提示
                    if (this.queryParams.pageSize === 'all') {
                        ElMessage.info('已切换到全部数据模式，将显示所有搜索结果（最多5000条）');
                    }

                    this.executeQuery();
                },

                handleGroupingChange(enabled) {
                    // 当启用分组时，提示用户将使用全部数据
                    if (enabled && this.queryParams.searchKeyword.includes(',')) {
                        ElMessage.info('分组模式已启用，将自动展示全部数据以完整显示分组结果');
                    }
                },

                initializeExpandedStates() {
                    // 创建新的状态对象
                    const newGroupExpandedState = {};
                    const newSourceExpandedState = {};

                    // 初始化分组展开状态
                    Object.keys(this.groupedResults).forEach(groupKey => {
                        newGroupExpandedState[groupKey] = true;

                        // 初始化来源展开状态
                        const groupData = this.groupedResults[groupKey];
                        if (groupData && groupData.sources) {
                            newSourceExpandedState[groupKey] = {};
                            Object.keys(groupData.sources).forEach(sourceKey => {
                                newSourceExpandedState[groupKey][sourceKey] = true;
                            });
                        }
                    });

                    // 一次性更新状态
                    this.groupExpandedState = newGroupExpandedState;
                    this.sourceExpandedState = newSourceExpandedState;

                    // 强制更新视图
                    this.$nextTick(() => {
                        this.$forceUpdate();
                    });
                },

                getContentSourceLabel(value) {
                    const labels = {
                        'files_v2': '文件内容',
                        'text': '文本内容'
                    };
                    return labels[value] || value;
                }
            },
            
            mounted() {
                ElMessage.info('欢迎使用数据库查询系统！请设置查询参数后点击执行查询。');
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>

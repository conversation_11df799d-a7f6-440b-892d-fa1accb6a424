{"version": 1.1, "tags": [{"name": "el-button", "description": "Button component from Element Plus", "attributes": [{"name": "type", "description": "Button type"}, {"name": "size", "description": "Button size"}, {"name": "loading", "description": "Loading state"}, {"name": "@click", "description": "Click event handler"}]}, {"name": "el-input", "description": "Input component from Element Plus", "attributes": [{"name": "v-model", "description": "Two-way binding"}, {"name": "placeholder", "description": "Placeholder text"}, {"name": "clearable", "description": "Show clear button"}, {"name": "prefix-icon", "description": "Prefix icon"}]}, {"name": "el-select", "description": "Select component from Element Plus", "attributes": [{"name": "v-model", "description": "Two-way binding"}, {"name": "placeholder", "description": "Placeholder text"}, {"name": "clearable", "description": "Show clear button"}, {"name": "@change", "description": "Change event handler"}]}, {"name": "el-option", "description": "Option component for el-select", "attributes": [{"name": "label", "description": "Option label"}, {"name": "value", "description": "Option value"}]}, {"name": "el-table", "description": "Table component from Element Plus", "attributes": [{"name": "data", "description": "Table data"}, {"name": "stripe", "description": "Striped table"}, {"name": "style", "description": "CSS styles"}, {"name": "header-cell-style", "description": "Header cell styles"}]}, {"name": "el-table-column", "description": "Table column component from Element Plus", "attributes": [{"name": "prop", "description": "Data property key"}, {"name": "label", "description": "Column header text"}, {"name": "width", "description": "Column width"}, {"name": "min-width", "description": "Minimum column width"}, {"name": "align", "description": "Text alignment"}]}, {"name": "el-form", "description": "Form component from Element Plus"}, {"name": "el-form-item", "description": "Form item component from Element Plus", "attributes": [{"name": "label", "description": "Form item label"}]}, {"name": "el-dialog", "description": "Dialog component from Element Plus", "attributes": [{"name": "v-model", "description": "Dialog visibility"}, {"name": "title", "description": "Dialog title"}, {"name": "width", "description": "Dialog width"}, {"name": "before-close", "description": "Before close callback"}]}, {"name": "el-pagination", "description": "Pagination component from Element Plus", "attributes": [{"name": "current-page", "description": "Current page number"}, {"name": "page-size", "description": "Items per page"}, {"name": "total", "description": "Total items"}, {"name": "layout", "description": "Pagination layout"}, {"name": "@current-change", "description": "Page change handler"}, {"name": "@size-change", "description": "Page size change handler"}]}, {"name": "el-tag", "description": "Tag component from Element Plus", "attributes": [{"name": "type", "description": "Tag type"}, {"name": "size", "description": "Tag size"}]}, {"name": "el-icon", "description": "Icon component from Element Plus"}, {"name": "el-switch", "description": "Switch component from Element Plus", "attributes": [{"name": "v-model", "description": "Switch value"}, {"name": "active-text", "description": "Text when active"}, {"name": "inactive-text", "description": "Text when inactive"}, {"name": "active-value", "description": "Value when active"}, {"name": "inactive-value", "description": "Value when inactive"}, {"name": "@change", "description": "Change event handler"}]}, {"name": "el-tooltip", "description": "Tooltip component from Element Plus", "attributes": [{"name": "content", "description": "Tooltip content"}, {"name": "placement", "description": "Tooltip placement"}]}], "globalAttributes": [{"name": "v-model", "description": "Vue.js two-way data binding"}, {"name": "v-if", "description": "Vue.js conditional rendering"}, {"name": "v-else", "description": "Vue.js else condition"}, {"name": "v-else-if", "description": "Vue.js else-if condition"}, {"name": "v-show", "description": "Vue.js show/hide element"}, {"name": "v-for", "description": "Vue.js list rendering"}, {"name": "v-html", "description": "Vue.js raw HTML rendering"}, {"name": "@click", "description": "Vue.js click event handler"}, {"name": "@change", "description": "Vue.js change event handler"}]}
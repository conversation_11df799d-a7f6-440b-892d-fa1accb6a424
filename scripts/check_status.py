#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目状态检查脚本
检查优化后的系统状态
"""

import os
import sys
import requests
import json
from pathlib import Path
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🔍 数据库查询系统状态检查")
    print("优化版 v2.0")
    print("=" * 60)

def check_files():
    """检查关键文件"""
    print("\n📁 检查关键文件...")
    
    required_files = [
        'app/main.py',
        'app/config/settings.py', 
        'app/core/error_handler.py',
        'app/core/logger.py',
        'app/core/monitor.py',
        'app/services/es_service.py',
        'frontend/index.html',
        '.env',
        'requirements.txt',
        'scripts/start.sh',
        'scripts/start.bat'
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有关键文件存在")
    return True

def check_directories():
    """检查目录结构"""
    print("\n📂 检查目录结构...")
    
    required_dirs = [
        'logs',
        'src/components',
        '.kiro/config'
    ]
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  ❌ {dir_path}/")
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"  🔧 已创建: {dir_path}/")
    
    print("✅ 目录结构正常")

def check_python_modules():
    """检查Python模块"""
    print("\n🐍 检查Python模块...")
    
    modules = [
        'flask',
        'flask_cors',
        'elasticsearch', 
        'dotenv',  # python-dotenv的导入名是dotenv
        'psutil',
        'requests'
        # 'pandas'  # 暂时跳过pandas检查，避免兼容性问题
    ]
    
    missing_modules = []
    for module in modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️  缺少模块: {', '.join(missing_modules)}")
        print("运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有Python模块可用")
    return True

def check_app_import():
    """检查应用导入"""
    print("\n🧪 检查应用导入...")
    
    try:
        sys.path.append('.')
        from app.config import config_manager
        print("  ✅ config模块")
        
        from app.core import LoggerConfig
        print("  ✅ logger模块")
        
        from app.core import handle_errors
        print("  ✅ error_handler模块")
        
        from app.core import performance_monitor
        print("  ✅ monitor模块")
        
        from app.services import OptimizedESService
        print("  ✅ es_service模块")
        
        # 不导入main模块，避免启动服务器
        print("✅ 所有模块导入成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False

def check_config():
    """检查配置"""
    print("\n⚙️  检查配置...")
    
    if not Path('.env').exists():
        print("  ❌ .env文件不存在")
        return False
    
    # 读取配置
    from dotenv import load_dotenv
    load_dotenv()
    
    config_items = [
        ('ES_HOST', os.getenv('ES_HOST')),
        ('ES_PORT', os.getenv('ES_PORT')),
        ('ES_USERNAME', os.getenv('ES_USERNAME')),
        ('ES_INDEX', os.getenv('ES_INDEX')),
        ('APP_HOST', os.getenv('APP_HOST')),
        ('APP_PORT', os.getenv('APP_PORT'))
    ]
    
    for key, value in config_items:
        if value:
            print(f"  ✅ {key}: {value}")
        else:
            print(f"  ⚠️  {key}: 未设置")
    
    print("✅ 配置检查完成")
    return True

def check_service_health():
    """检查服务健康状态"""
    print("\n🏥 检查服务健康状态...")
    
    app_host = os.getenv('APP_HOST', '127.0.0.1')
    app_port = os.getenv('APP_PORT', '8081')
    health_url = f"http://{app_host}:{app_port}/api/health"

    # 从环境变量获取健康检查超时配置
    timeout = int(os.getenv('HEALTH_CHECK_TIMEOUT', '5'))

    try:
        response = requests.get(health_url, timeout=timeout)
        if response.status_code == 200:
            health_data = response.json()
            print(f"  ✅ 服务状态: {health_data.get('status', 'unknown')}")
            
            # 显示详细信息
            if 'services' in health_data:
                services = health_data['services']
                if 'elasticsearch' in services:
                    es_status = services['elasticsearch'].get('status', 'unknown')
                    print(f"  ✅ Elasticsearch: {es_status}")
                
                if 'application' in services:
                    app_status = services['application'].get('status', 'unknown')
                    print(f"  ✅ 应用状态: {app_status}")
            
            return True
        else:
            print(f"  ❌ 服务响应异常: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ⚠️  服务未启动或无法连接")
        return False
    except Exception as e:
        print(f"  ❌ 健康检查失败: {e}")
        return False

def generate_report():
    """生成状态报告"""
    print("\n📊 生成状态报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'version': '2.0',
        'checks': {
            'files': check_files(),
            'python_modules': check_python_modules(),
            'app_import': check_app_import(),
            'config': check_config()
        }
    }
    
    # 检查服务状态（如果服务正在运行）
    service_status = check_service_health()
    report['checks']['service_health'] = service_status
    
    # 保存报告
    report_file = Path('logs/status_report.json')
    report_file.parent.mkdir(exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 状态报告已保存: {report_file}")
    
    # 显示总结
    total_checks = len(report['checks'])
    passed_checks = sum(1 for v in report['checks'].values() if v)
    
    print(f"\n📋 检查总结: {passed_checks}/{total_checks} 通过")
    
    if passed_checks == total_checks:
        print("🎉 系统状态良好！")
        return True
    else:
        print("⚠️  系统存在问题，请检查上述输出")
        return False

def main():
    """主函数"""
    print_header()
    
    check_directories()
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    # 生成报告
    success = generate_report()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 状态检查完成 - 系统正常")
    else:
        print("⚠️  状态检查完成 - 发现问题")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
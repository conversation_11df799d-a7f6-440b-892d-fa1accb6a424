#!/bin/bash

# 数据库查询系统启动/停止脚本
# 优化版 v2.1 - 统一启动/停止脚本

set -e  # 遇到错误立即退出

# PID文件路径
PID_FILE="./.pid"

# 脚本参数处理
QUICK_MODE=false
CLEAN_MODE=false
CHECK_MODE=false
STOP_MODE=false
STATUS_MODE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick|-q)
            QUICK_MODE=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --check)
            CHECK_MODE=true
            shift
            ;;
        --stop)
            STOP_MODE=true
            shift
            ;;
        --status)
            STATUS_MODE=true
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --quick, -q    快速启动模式（跳过检查）"
            echo "  --clean, -c    清理模式（清理缓存后启动）"
            echo "  --check        仅执行系统检查"
            echo "  --stop         停止运行中的应用"
            echo "  --status       查看应用运行状态"
            echo "  --help, -h     显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印横幅
print_banner() {
    echo -e "${BLUE}🔍 数据库查询系统 v2.1${NC}"
    
    if [ "$QUICK_MODE" = true ]; then
        echo -e "${YELLOW}⚡ 快速启动模式${NC}"
    elif [ "$CLEAN_MODE" = true ]; then
        echo -e "${YELLOW}🧹 清理启动模式${NC}"
    elif [ "$CHECK_MODE" = true ]; then
        echo -e "${YELLOW}🔍 系统检查模式${NC}"
    elif [ "$STOP_MODE" = true ]; then
        echo -e "${YELLOW}🛑 停止应用模式${NC}"
    elif [ "$STATUS_MODE" = true ]; then
        echo -e "${YELLOW}📊 状态查询模式${NC}"
    fi
    echo ""
}

# 检查应用状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            echo -e "${GREEN}✅ 应用正在运行 (PID: $pid)${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️ PID文件存在，但进程不存在 (PID: $pid)${NC}"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️ 应用未运行${NC}"
        return 1
    fi
}

# 停止应用
stop_application() {
    echo -e "${CYAN}🛑 正在停止应用...${NC}"

    local pids_to_kill=()
    local stopped_count=0

    # 方法1: 从PID文件获取进程ID
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            pids_to_kill+=("$pid")
            echo -e "${CYAN}  📋 从PID文件找到进程: $pid${NC}"
        else
            echo -e "${YELLOW}  ⚠️ PID文件中的进程已不存在: $pid${NC}"
            rm -f "$PID_FILE"
        fi
    fi

    # 方法2: 查找运行中的run.py进程
    local running_pids=$(ps aux | grep -i "run\.py" | grep -v grep | awk '{print $2}')
    if [ -n "$running_pids" ]; then
        for pid in $running_pids; do
            if [[ ! " ${pids_to_kill[@]} " =~ " ${pid} " ]]; then
                pids_to_kill+=("$pid")
                echo -e "${CYAN}  🔍 发现运行中的应用进程: $pid${NC}"
            fi
        done
    fi

    # 如果没有找到任何进程
    if [ ${#pids_to_kill[@]} -eq 0 ]; then
        echo -e "${YELLOW}⚠️ 未找到运行中的应用进程${NC}"
        rm -f "$PID_FILE"
        return 1
    fi

    # 停止所有找到的进程
    for pid in "${pids_to_kill[@]}"; do
        echo -e "${CYAN}  🛑 正在停止进程 $pid...${NC}"

        # 首先尝试优雅停止 (SIGTERM)
        if kill -15 "$pid" 2>/dev/null; then
            # 等待进程结束
            local count=0
            while ps -p "$pid" > /dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done

            # 如果进程仍在运行，强制终止
            if ps -p "$pid" > /dev/null; then
                echo -e "${YELLOW}    ⚠️ 优雅停止失败，强制终止进程 $pid${NC}"
                kill -9 "$pid" 2>/dev/null
                sleep 1
            fi

            # 检查是否成功停止
            if ! ps -p "$pid" > /dev/null; then
                echo -e "${GREEN}    ✅ 进程 $pid 已停止${NC}"
                stopped_count=$((stopped_count + 1))
            else
                echo -e "${RED}    ❌ 无法停止进程 $pid${NC}"
            fi
        else
            echo -e "${YELLOW}    ⚠️ 进程 $pid 可能已经停止${NC}"
        fi
    done

    # 清理PID文件
    rm -f "$PID_FILE"

    if [ $stopped_count -gt 0 ]; then
        echo -e "${GREEN}✅ 成功停止 $stopped_count 个应用进程${NC}"
        return 0
    else
        echo -e "${RED}❌ 未能停止任何进程${NC}"
        return 1
    fi
}

# 清理项目缓存
clean_project() {
    echo -e "${CYAN}🧹 清理项目缓存...${NC}"
    
    # 清理Python缓存文件
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    # 清理临时文件
    find . -name "*~" -delete 2>/dev/null || true
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "*.temp" -delete 2>/dev/null || true
    find . -name ".DS_Store" -delete 2>/dev/null || true
    
    echo -e "${GREEN}✅ 项目缓存清理完成${NC}"
}

# 检查Python版本
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ 未找到Python，请先安装Python 3.8+${NC}"
        exit 1
    fi
    
    # 检查版本是否满足要求
    if ! $PYTHON_CMD -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        echo -e "${RED}❌ Python版本过低，需要3.8+${NC}"
        exit 1
    fi
}

# 检查并创建必要目录
create_directories() {
    directories=("logs" "frontend/assets" "tests")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
        fi
    done
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
        else
            echo -e "${RED}❌ .env和.env.example文件都不存在${NC}"
            exit 1
        fi
    fi
}

# 安装依赖
install_dependencies() {
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ requirements.txt文件不存在${NC}"
        exit 1
    fi
    
    # 检查是否需要安装依赖
    if $PYTHON_CMD -c "
import pkg_resources
import sys

try:
    with open('requirements.txt', 'r') as f:
        requirements = f.read().splitlines()
    
    for requirement in requirements:
        if requirement.strip() and not requirement.startswith('#'):
            pkg_resources.require(requirement.strip())
    print('all_installed')
except Exception as e:
    print('need_install')
" | grep -q "need_install"; then
        $PYTHON_CMD -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple > /dev/null 2>&1
    fi
}

# 测试ES连接
test_es_connection() {
    # 从.env文件读取ES配置
    if [ -f ".env" ]; then
        source .env
    fi

    # 检查必需的环境变量
    if [ -z "$ES_HOST" ] || [ -z "$ES_USERNAME" ] || [ -z "$ES_PASSWORD" ]; then
        echo -e "${RED}❌ 缺少必需的ES环境变量 (ES_HOST, ES_USERNAME, ES_PASSWORD)${NC}"
        return 1
    fi

    ES_PORT=${ES_PORT:-"9200"}

    # 测试连接
    curl -s --connect-timeout 5 -u "$ES_USERNAME:$ES_PASSWORD" \
        "http://$ES_HOST:$ES_PORT/_cluster/health" > /dev/null 2>&1
}

# 测试应用导入
test_app_import() {
    $PYTHON_CMD -c "
import sys
sys.path.append('.')
try:
    from app.main import create_app
except Exception as e:
    import traceback
    traceback.print_exc()
    sys.exit(1)
" 2>/dev/null || { echo -e "${RED}❌ 应用模块测试失败${NC}"; exit 1; }
}

# 系统状态检查
system_check() {
    echo -e "${CYAN}📊 执行系统状态检查...${NC}"
    
    # 检查关键文件
    local required_files=(
        "app/main.py"
        "app/config/settings.py"
        "app/core/error_handler.py"
        "app/core/logger.py"
        "app/core/monitor.py"
        "app/services/es_service.py"
        "frontend/index.html"
        ".env"
        "requirements.txt"
        "run.py"
    )
    
    local missing_files=()
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}  ✅ $file${NC}"
        else
            echo -e "${RED}  ❌ $file${NC}"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}❌ 缺少关键文件: ${missing_files[*]}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 所有关键文件存在${NC}"
    return 0
}

# 启动应用
start_application() {
    echo -e "${CYAN}🚀 启动应用服务器...${NC}"
    
    # 检查应用是否已经在运行
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            echo -e "${YELLOW}⚠️ 应用已经在运行中 (PID: $pid)${NC}"
            echo -e "${YELLOW}   如需重启，请先使用 $0 --stop 停止应用${NC}"
            return 1
        else
            rm -f "$PID_FILE"
        fi
    fi
    
    # 从.env文件读取配置
    if [ -f ".env" ]; then
        source .env
    fi

    # 检查必需的环境变量
    if [ -z "$APP_HOST" ] || [ -z "$APP_PORT" ]; then
        echo -e "${RED}❌ 缺少必需的应用环境变量 (APP_HOST, APP_PORT)${NC}"
        return 1
    fi

    echo -e "${GREEN}📍 服务地址: http://$APP_HOST:$APP_PORT${NC}"
    
    # 启动应用（后台运行）
    nohup $PYTHON_CMD run.py >> ./logs/app.log 2>&1 &

    # 保存PID
    local app_pid=$!
    echo $app_pid > "$PID_FILE"

    # 等待一下确保应用启动
    sleep 2

    # 检查进程是否还在运行
    if ps -p "$app_pid" > /dev/null; then
        echo -e "${GREEN}✅ 应用已启动 (PID: $app_pid)${NC}"
        return 0
    else
        echo -e "${RED}❌ 应用启动失败，请检查日志文件${NC}"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 收到中断信号，正在停止...${NC}"
    stop_application
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    print_banner
    
    # 状态检查模式
    if [ "$STATUS_MODE" = true ]; then
        check_status
        return $?
    fi
    
    # 停止模式
    if [ "$STOP_MODE" = true ]; then
        stop_application
        return $?
    fi
    
    # 清理模式
    if [ "$CLEAN_MODE" = true ]; then
        clean_project
    fi
    
    # 仅检查模式
    if [ "$CHECK_MODE" = true ]; then
        check_python
        create_directories
        check_env_file
        system_check
        test_app_import
        echo -e "${GREEN}✅ 系统检查完成${NC}"
        return 0
    fi
    
    # 快速启动模式
    if [ "$QUICK_MODE" = true ]; then
        # 只做基本检查
        check_python
        create_directories
        start_application
        return 0
    fi
    
    # 完整启动模式（默认）
    check_python
    create_directories
    check_env_file
    install_dependencies
    test_es_connection
    test_app_import
    
    # 启动应用
    start_application
}

# 检查是否直接运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
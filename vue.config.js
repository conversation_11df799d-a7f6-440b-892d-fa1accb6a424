{
  "$schema": "https://raw.githubusercontent.com/vuejs/language-tools/master/extensions/vscode/schemas/vue-tsconfig.schema.json",
  "extends": "./jsconfig.json",
  "compilerOptions": {
    "jsx": "preserve",
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "allowJs": true,
    "strict": false,
    "types": ["element-plus/global"]
  },
  "vueCompilerOptions": {
    "target": 3.0,
    "extensions": [".vue"],
    "experimentalDisableTemplateSupport": false,
    "experimentalTemplateCompilerOptions": {
      "isCustomElement": (tag) => tag.startsWith('el-')
    }
  },
  "include": [
    "frontend/**/*.vue",
    "frontend/**/*.js",
    "frontend/**/*.ts"
  ]
}
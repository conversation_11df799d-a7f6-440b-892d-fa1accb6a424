{"name": "database-query-system", "version": "1.0.0", "description": "Database Query System with Vue.js and Element Plus", "main": "index.js", "scripts": {"dev": "python3 run.py", "start": "python3 run.py"}, "dependencies": {"vue": "^3.4.0", "element-plus": "^2.4.0", "axios": "^1.6.0"}, "devDependencies": {"@vue/language-server": "^1.8.0"}, "keywords": ["vue", "element-plus", "database", "elasticsearch"], "author": "", "license": "MIT"}
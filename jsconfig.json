{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["frontend/*"]}, "types": ["element-plus/global"]}, "include": ["frontend/**/*", "frontend/**/*.vue", "frontend/**/*.js", "frontend/**/*.ts"], "exclude": ["node_modules", "app", "logs", "tests"], "vueCompilerOptions": {"target": 3.0}}
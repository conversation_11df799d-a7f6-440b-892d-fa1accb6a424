// Global type declarations for Vue 3 + Element Plus project

// Vue 3 Global API
declare const Vue: any;
declare const { createApp, ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } = Vue;

// Element Plus Global Components
declare const ElementPlus: any;
declare const { ElMessage, ElMessageBox, ElNotification, ElLoading } = ElementPlus;

// Axios
declare const axios: any;

// Vue Application Instance Types
interface App {
  mount(rootContainer: string | Element): ComponentPublicInstance;
  use(plugin: any, ...options: any[]): App;
}

// Component Props Types
interface ComponentProps {
  [key: string]: any;
}

// Vue Component Instance
interface ComponentPublicInstance {
  [key: string]: any;
}

// Common Data Types for the Project
interface QueryParams {
  searchKeyword: string;
  idSearch: string;
  contentSourceFilter: string | null;
  orderBy: string;
  pageSize: number | string;
  tableName: string;
  enableGrouping: boolean;
}

interface QueryResult {
  id: string | number;
  title: string;
  content: string;
  dynamic_content?: string;
  dynamic_content_highlighted?: string;
  content_highlighted?: string;
  title_highlighted?: string;
  MonitorDate: string;
  content_source: string;
  createdAt: string;
  url?: string;
  matched_keywords?: string[];
  _score?: number;
  public_time?: string;
}

interface GroupedResult {
  keywords: string[];
  total_count: number;
  sources: {
    [key: string]: {
      source_name: string;
      count: number;
      records: QueryResult[];
    };
  };
}

interface Pagination {
  current_page: number;
  page_size: number | string;
  total_count: number;
}

// Global Variables Available in Templates
declare global {
  const queryParams: QueryParams;
  const queryResults: QueryResult[];
  const groupedResults: { [key: string]: GroupedResult };
  const pagination: Pagination;
  const loading: boolean;
  const hasSearched: boolean;
  const isGroupedView: boolean;
  const totalRecords: number;
  const detailDialogVisible: boolean;
  const currentRecord: QueryResult | null;
  const fullContentDialogVisible: boolean;
  const fullContentTitle: string;
  const fullContentValue: string;
  const showRawData: boolean;
  const groupExpandedState: { [key: string]: boolean };
  const sourceExpandedState: { [key: string]: { [key: string]: boolean } };
  const formatExecutionTime: string;
  
  // Methods
  const executeQuery: () => void;
  const handlePageSizeChange: () => void;
  const handleGroupingChange: (enabled: boolean) => void;
  const handleSizeChange: (size: number) => void;
  const handleCurrentChange: (page: number) => void;
  const viewRecord: (record: QueryResult) => void;
  const openUrl: (url: string) => void;
  const toggleGroup: (groupKey: string) => void;
  const toggleSource: (groupKey: string, sourceKey: string) => void;
  const getSourceExpandedState: (groupKey: string, sourceKey: string) => boolean;
  const getContentSourceLabel: (source: string) => string;
  const getSourceTagType: (source: string) => string;
  const getSourceLabel: (source: string) => string;
  const getMainContent: (record: QueryResult) => string;
  const formatDate: (dateString: string) => string;
  const isImportantField: (fieldName: string) => boolean;
  const isLongContent: (value: any) => boolean;
  const showFullContent: (title: string, content: string) => void;
  const copyFullData: () => void;
  const copyMainContent: () => void;
  const copySpecificContent: () => void;
  const toggleDataView: () => void;
  const handleDetailClose: () => void;
  const handleFullContentClose: () => void;
}

export {};
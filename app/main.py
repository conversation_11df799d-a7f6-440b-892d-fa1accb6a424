#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询系统主应用
Database Query System Main Application
"""

import os
from flask import Flask, send_from_directory
from flask_cors import CORS
from dotenv import load_dotenv

# 导入应用模块
from app.config import config_manager
from app.core import LoggerConfig, get_logger
from app.services import OptimizedESService, record_api
from app.api import query_bp, health_bp

# 加载环境变量
load_dotenv()

def create_app():
    """应用工厂函数"""
    
    # 初始化日志系统
    logger_config = LoggerConfig(
        log_level=config_manager.app_config.log_level,
        log_dir=os.getenv('LOG_DIR', 'logs')
    )
    logger_config.setup_logging()
    
    # 获取应用日志器
    logger = get_logger(__name__)
    
    # 验证配置
    if not config_manager.validate_config():
        logger.error("配置验证失败")
        exit(1)
    
    # 创建Flask应用
    app = Flask(__name__)
    CORS(app)
    
    # 初始化ES服务
    es_service = OptimizedESService(config_manager.get_es_config_dict())
    app.es_service = es_service
    
    # 注册蓝图
    app.register_blueprint(record_api)
    app.register_blueprint(query_bp, url_prefix='/api')
    app.register_blueprint(health_bp, url_prefix='/api')
    
    # 静态文件路由
    @app.route('/')
    def index():
        """提供前端页面"""
        # 使用绝对路径
        frontend_dir = os.path.join(os.getcwd(), 'frontend')
        logger.info(f"前端目录路径: {frontend_dir}")
        if os.path.exists(os.path.join(frontend_dir, 'index.html')):
            logger.info("找到前端index.html文件")
        else:
            logger.error(f"前端index.html文件不存在! 路径: {os.path.join(frontend_dir, 'index.html')}")
        return send_from_directory(frontend_dir, 'index.html')
    
    @app.route('/assets/<path:filename>')
    def assets(filename):
        """静态资源"""
        assets_dir = os.path.join(os.getcwd(), 'frontend', 'assets')
        logger.info(f"访问资源: {filename}, 路径: {assets_dir}")
        return send_from_directory(assets_dir, filename)
    
    # 添加静态文件服务
    @app.route('/<path:filename>')
    def static_files(filename):
        """处理其他静态文件"""
        frontend_dir = os.path.join(os.getcwd(), 'frontend')
        logger.info(f"访问静态文件: {filename}, 路径: {frontend_dir}")
        if os.path.exists(os.path.join(frontend_dir, filename)):
            return send_from_directory(frontend_dir, filename)
        else:
            logger.error(f"静态文件不存在: {filename}")
            return "File not found", 404
    
    logger.info("应用初始化完成", extra={
        'es_host': config_manager.es_config.host,
        'app_port': config_manager.app_config.port
    })
    
    return app

def main():
    """主函数"""
    app = create_app()
    logger = get_logger(__name__)
    
    logger.info("🚀 启动数据库查询系统...")
    logger.info("📊 Elasticsearch查询API服务 (优化版 v2.0)")
    logger.info(f"🌐 访问地址: http://{config_manager.app_config.host}:{config_manager.app_config.port}")
    logger.info("📋 API文档:")
    logger.info("  - POST /api/query - 执行查询")
    logger.info("  - GET  /api/statistics - 获取统计信息")
    logger.info("  - POST /api/scroll-search - 滚动搜索")
    logger.info("  - GET  /api/health - 健康检查")
    logger.info("  - GET  /api/metrics - 性能指标")
    logger.info("-" * 50)
    
    try:
        app.run(
            debug=config_manager.app_config.debug,
            host=config_manager.app_config.host,
            port=config_manager.app_config.port
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}", exc_info=True)

if __name__ == '__main__':
    main()
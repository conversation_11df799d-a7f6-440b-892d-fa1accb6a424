#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控模块
"""

import os
import time
import psutil
import threading
from datetime import datetime
from collections import deque
from dataclasses import dataclass
from typing import Dict, List, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    response_time: float
    request_count: int
    error_count: int

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, max_history: int = None):
        # 从环境变量获取配置
        self.max_history = max_history or int(os.getenv('MONITOR_MAX_HISTORY', '1000'))
        self.request_history_size = int(os.getenv('MONITOR_REQUEST_HISTORY', '100'))

        self.metrics_history = deque(maxlen=self.max_history)
        self.request_times = deque(maxlen=self.request_history_size)
        self.error_count = 0
        self.total_requests = 0
        self._lock = threading.Lock()

        # 从环境变量获取阈值配置
        self.cpu_warning_threshold = int(os.getenv('MONITOR_CPU_WARNING_THRESHOLD', '80'))
        self.memory_warning_threshold = int(os.getenv('MONITOR_MEMORY_WARNING_THRESHOLD', '85'))
        self.error_rate_threshold = int(os.getenv('MONITOR_ERROR_RATE_THRESHOLD', '5'))
        self.response_time_warning = int(os.getenv('MONITOR_RESPONSE_TIME_WARNING', '2000'))
        
    def record_request(self, response_time: float, is_error: bool = False):
        """记录请求性能"""
        with self._lock:
            self.total_requests += 1
            self.request_times.append(response_time)
            
            if is_error:
                self.error_count += 1
            
            # 记录系统指标
            metric = PerformanceMetric(
                timestamp=datetime.now(),
                cpu_percent=psutil.cpu_percent(),
                memory_percent=psutil.virtual_memory().percent,
                response_time=response_time,
                request_count=self.total_requests,
                error_count=self.error_count
            )
            
            self.metrics_history.append(metric)
    
    def get_current_stats(self) -> Dict:
        """获取当前统计信息"""
        with self._lock:
            if not self.request_times:
                return {
                    'avg_response_time': 0,
                    'min_response_time': 0,
                    'max_response_time': 0,
                    'total_requests': self.total_requests,
                    'error_rate': 0,
                    'cpu_percent': psutil.cpu_percent(),
                    'memory_percent': psutil.virtual_memory().percent
                }
            
            avg_time = sum(self.request_times) / len(self.request_times)
            min_time = min(self.request_times)
            max_time = max(self.request_times)
            error_rate = (self.error_count / self.total_requests * 100) if self.total_requests > 0 else 0
            
            return {
                'avg_response_time': round(avg_time, 2),
                'min_response_time': round(min_time, 2),
                'max_response_time': round(max_time, 2),
                'total_requests': self.total_requests,
                'error_count': self.error_count,
                'error_rate': round(error_rate, 2),
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'requests_per_minute': self._calculate_rpm()
            }
    
    def _calculate_rpm(self) -> float:
        """计算每分钟请求数"""
        if len(self.metrics_history) < 2:
            return 0
        
        recent_metrics = list(self.metrics_history)[-60:]  # 最近60个指标
        if len(recent_metrics) < 2:
            return 0
        
        time_span = (recent_metrics[-1].timestamp - recent_metrics[0].timestamp).total_seconds()
        if time_span == 0:
            return 0
        
        request_count = recent_metrics[-1].request_count - recent_metrics[0].request_count
        return round((request_count / time_span) * 60, 2)
    
    def get_health_status(self) -> Dict:
        """获取健康状态"""
        stats = self.get_current_stats()
        
        # 健康状态判断
        status = "healthy"
        issues = []

        if stats['cpu_percent'] > self.cpu_warning_threshold:
            status = "warning"
            issues.append("CPU使用率过高")

        if stats['memory_percent'] > self.memory_warning_threshold:
            status = "warning"
            issues.append("内存使用率过高")

        if stats['error_rate'] > self.error_rate_threshold:
            status = "error"
            issues.append("错误率过高")

        if stats['avg_response_time'] > self.response_time_warning:
            status = "warning"
            issues.append("响应时间过长")
        
        return {
            'status': status,
            'issues': issues,
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        }

# 全局监控实例
performance_monitor = PerformanceMonitor()

def monitor_performance(func):
    """性能监控装饰器"""
    from functools import wraps
    
    @wraps(func)
    def performance_wrapper(*args, **kwargs):
        start_time = time.time()
        is_error = False
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            is_error = True
            raise e
        finally:
            response_time = (time.time() - start_time) * 1000
            performance_monitor.record_request(response_time, is_error)
    
    return performance_wrapper
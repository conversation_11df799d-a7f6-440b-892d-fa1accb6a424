#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加额外字段
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'execution_time'):
            log_entry['execution_time'] = record.execution_time
        
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)

class LoggerConfig:
    """日志配置类"""

    def __init__(self, log_level: str = None, log_dir: str = None):
        # 从环境变量获取配置，如果没有则使用传入参数或默认值
        self.log_level = getattr(logging, (log_level or os.getenv('LOG_LEVEL', 'INFO')).upper())
        self.log_dir = Path(log_dir or os.getenv('LOG_DIR', 'logs'))
        self.log_dir.mkdir(exist_ok=True)

        # 从环境变量获取日志文件配置
        self.log_max_bytes = int(os.getenv('LOG_MAX_BYTES', '10485760'))  # 10MB
        self.log_backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))
        self.log_error_backup_count = int(os.getenv('LOG_ERROR_BACKUP_COUNT', '5'))
        self.log_perf_backup_count = int(os.getenv('LOG_PERF_BACKUP_COUNT', '3'))
        self.log_access_max_bytes = int(os.getenv('LOG_ACCESS_MAX_BYTES', '5242880'))  # 5MB
        self.log_access_backup_count = int(os.getenv('LOG_ACCESS_BACKUP_COUNT', '3'))
        
    def setup_logging(self):
        """设置日志配置"""
        # 创建根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器 - 应用日志
        app_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'app.log',
            maxBytes=self.log_max_bytes,
            backupCount=self.log_backup_count,
            encoding='utf-8'
        )
        app_handler.setLevel(self.log_level)
        app_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(app_handler)
        
        # 错误日志处理器
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'error.log',
            maxBytes=self.log_max_bytes,
            backupCount=self.log_error_backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(error_handler)
        
        # 性能日志处理器
        perf_logger = logging.getLogger('performance')
        perf_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'performance.log',
            maxBytes=self.log_max_bytes,
            backupCount=self.log_perf_backup_count,
            encoding='utf-8'
        )
        perf_handler.setLevel(logging.INFO)
        perf_handler.setFormatter(JSONFormatter())
        perf_logger.addHandler(perf_handler)
        perf_logger.setLevel(logging.INFO)
        perf_logger.propagate = False
        
        # ES查询日志处理器
        es_logger = logging.getLogger('elasticsearch')
        es_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'elasticsearch.log',
            maxBytes=self.log_access_max_bytes,
            backupCount=self.log_access_backup_count,
            encoding='utf-8'
        )
        es_handler.setLevel(logging.INFO)
        es_handler.setFormatter(JSONFormatter())
        es_logger.addHandler(es_handler)
        es_logger.setLevel(logging.INFO)
        es_logger.propagate = False
        
        logging.info("日志系统初始化完成")

def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)

# 性能日志记录器
def log_performance(func_name: str, execution_time: float, **kwargs):
    """记录性能日志"""
    perf_logger = logging.getLogger('performance')
    perf_logger.info(
        f"函数 {func_name} 执行完成",
        extra={
            'execution_time': execution_time,
            'function_name': func_name,
            **kwargs
        }
    )

# ES查询日志记录器
def log_es_query(query_type: str, execution_time: float, result_count: int, **kwargs):
    """记录ES查询日志"""
    es_logger = logging.getLogger('elasticsearch')
    es_logger.info(
        f"ES查询完成: {query_type}",
        extra={
            'query_type': query_type,
            'execution_time': execution_time,
            'result_count': result_count,
            **kwargs
        }
    )
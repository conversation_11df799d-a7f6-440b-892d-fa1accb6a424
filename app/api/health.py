#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查API蓝图
"""

from flask import Blueprint, jsonify, current_app
from datetime import datetime

from app.core import handle_errors, performance_monitor, get_logger
from app.config import config_manager

health_bp = Blueprint('health', __name__)
logger = get_logger(__name__)

@health_bp.route('/health', methods=['GET'])
@handle_errors
def health_check_api():
    """健康检查接口"""
    health_info = current_app.es_service.health_check()
    performance_stats = performance_monitor.get_health_status()
    
    overall_status = "healthy"
    if health_info['status'] != 'healthy' or performance_stats['status'] != 'healthy':
        overall_status = "unhealthy"
    
    return jsonify({
        'success': True,
        'status': overall_status,
        'timestamp': datetime.now().isoformat(),
        'services': {
            'elasticsearch': health_info,
            'application': performance_stats
        },
        'config': {
            'es_host': config_manager.es_config.host,
            'es_index': config_manager.es_config.index,
            'app_version': '2.0.0'
        }
    })

@health_bp.route('/metrics', methods=['GET'])
@handle_errors
def metrics_api():
    """性能指标接口"""
    stats = performance_monitor.get_current_stats()
    
    return jsonify({
        'success': True,
        'data': stats,
        'timestamp': datetime.now().isoformat()
    })
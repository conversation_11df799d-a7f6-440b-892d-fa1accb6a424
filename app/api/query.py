#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询API蓝图
"""

import time
from flask import Blueprint, request, jsonify, current_app
from datetime import datetime

from app.core import handle_errors, monitor_performance, get_logger
from app.core.logger import log_performance
from app.core.error_handler import validate_query_params

query_bp = Blueprint('query', __name__)
logger = get_logger(__name__)

@query_bp.route('/query', methods=['POST'])
@handle_errors
@monitor_performance
def api_query():
    """优化后的查询API接口"""
    start_time = time.time()
    
    # 获取并验证请求参数
    data = request.get_json() or {}
    validated_params = validate_query_params(data)
    
    logger.info("收到查询请求", extra={
        'search_keyword': validated_params['searchKeyword'],
        'id_search': validated_params['idSearch'],
        'content_source': validated_params['contentSourceFilter']
    })
    
    # 使用优化后的ES服务进行搜索
    search_result = current_app.es_service.search(
        search_keyword=validated_params['searchKeyword'],
        id_search=validated_params['idSearch'],
        order_by=validated_params['orderBy'],
        page=validated_params['page'],
        page_size=validated_params['pageSize'],
        content_source_filter=validated_params['contentSourceFilter'],
        date_range=validated_params['dateRange'],
        enable_highlight=True,
        enable_cache=True
    )
    
    execution_time = int((time.time() - start_time) * 1000)
    
    # 记录性能日志
    log_performance(
        'api_query',
        execution_time,
        result_count=len(search_result['data']),
        search_keyword=validated_params['searchKeyword']
    )
    
    # 提取结果和分页信息
    results = search_result['data']
    pagination_info = search_result['pagination']
    
    # 处理分组查询
    if validated_params['enableGrouping'] and ',' in validated_params['searchKeyword']:
        keywords = [kw.strip() for kw in validated_params['searchKeyword'].split(',') if kw.strip()]
        
        if len(keywords) > 1:
            # 使用优化后的分组逻辑
            grouped_results = group_results_optimized(results, keywords)
            
            total_records = len(results)
            total_groups = len(grouped_results)
            
            # 计算来源统计
            source_stats = {'files_v2': 0, 'text': 0}
            for group_data in grouped_results.values():
                for source_key, source_data in group_data['sources'].items():
                    source_stats[source_key] += source_data['count']
            
            return jsonify({
                'success': True,
                'message': f'查询成功，找到 {total_records} 条记录，分为 {total_groups} 个关键词组合',
                'data': grouped_results,
                'meta': {
                    'total_records': total_records,
                    'total_groups': total_groups,
                    'source_stats': source_stats,
                    'execution_time': execution_time,
                    'search_keyword': validated_params['searchKeyword'],
                    'keywords': keywords,
                    'grouped': True
                }
            })
    
    # 传统查询返回格式
    message = f'查询成功，找到 {len(results)} 条记录，总计 {pagination_info["total_count"]} 条'
    if pagination_info.get('show_all') and pagination_info["total_count"] > 5000:
        message += f'（全部数据模式已限制显示前5000条，共{pagination_info["total_count"]}条）'
    
    logger.info("查询完成", extra={
        'execution_time': execution_time,
        'result_count': len(results),
        'total_count': pagination_info["total_count"]
    })
    
    return jsonify({
        'success': True,
        'message': message,
        'data': results,
        'pagination': pagination_info,
        'meta': {
            'total': len(results),
            'execution_time': execution_time,
            'search_keyword': validated_params['searchKeyword'],
            'content_source_filter': validated_params['contentSourceFilter'],
            'date_range': validated_params['dateRange'],
            'grouped': False
        }
    })

@query_bp.route('/statistics', methods=['GET'])
@handle_errors
@monitor_performance
def get_statistics_api():
    """获取搜索统计信息"""
    search_keyword = request.args.get('keyword', '')
    stats = current_app.es_service.get_statistics(search_keyword)
    
    return jsonify({
        'success': True,
        'data': stats
    })

@query_bp.route('/scroll-search', methods=['POST'])
@handle_errors
@monitor_performance
def scroll_search_api():
    """滚动搜索API - 处理大量数据"""
    data = request.get_json() or {}
    search_keyword = data.get('searchKeyword', '')
    scroll_size = data.get('scrollSize', 1000)
    
    result = current_app.es_service.scroll_search(
        search_keyword=search_keyword,
        scroll_size=scroll_size
    )
    
    return jsonify({
        'success': True,
        'message': f'滚动搜索完成，共获取 {len(result["data"])} 条记录',
        'data': result['data'],
        'total_count': result['total_count'],
        'scroll_complete': result['scroll_complete']
    })

def group_results_optimized(results, keywords):
    """优化后的结果分组函数"""
    from itertools import combinations
    
    # 生成关键词组合
    keyword_combinations = []
    for i in range(1, len(keywords) + 1):
        for combo in combinations(keywords, i):
            keyword_combinations.append(list(combo))
    
    grouped_results = {}
    
    # 为每个关键词组合创建分组
    for combo in keyword_combinations:
        combo_key = ' + '.join(combo)
        grouped_results[combo_key] = {
            'keywords': combo,
            'total_count': 0,
            'sources': {
                'files_v2': {'records': [], 'count': 0, 'source_name': '文件内容'},
                'text': {'records': [], 'count': 0, 'source_name': '文本内容'}
            }
        }
    
    # 为每条记录检查匹配的关键词组合
    for record in results:
        # 使用动态内容和标题进行匹配
        record_text = ""
        
        if record.get('title'):
            record_text += str(record['title']).lower() + " "
        
        if record.get('dynamic_content'):
            record_text += str(record['dynamic_content']).lower() + " "
        elif record.get('content'):
            record_text += str(record['content']).lower() + " "
        
        # 检查每个组合是否匹配
        for combo in keyword_combinations:
            combo_key = ' + '.join(combo)
            
            # 检查组合中的所有关键词是否都在记录中
            if all(keyword.lower() in record_text for keyword in combo):
                record_copy = record.copy()
                record_copy['matched_keywords'] = combo
                record_copy['match_group'] = combo_key
                
                # 根据内容来源进行细分
                content_source = record_copy.get('content_source', 'text')
                if content_source in grouped_results[combo_key]['sources']:
                    grouped_results[combo_key]['sources'][content_source]['records'].append(record_copy)
                    grouped_results[combo_key]['sources'][content_source]['count'] += 1
                    grouped_results[combo_key]['total_count'] += 1
    
    # 移除空组
    return {k: v for k, v in grouped_results.items() if v['total_count'] > 0}
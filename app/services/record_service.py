#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单条记录详情查询API
"""

from flask import Blueprint, request, jsonify
from elasticsearch import Elasticsearch
import logging
import traceback
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建Blueprint
record_api = Blueprint('record_api', __name__)

# Elasticsearch 配置 - 从环境变量读取
def get_es_config():
    """从环境变量获取ES配置"""
    required_vars = ['ES_HOST', 'ES_USERNAME', 'ES_PASSWORD', 'ES_INDEX']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")

    return {
        'host': os.getenv('ES_HOST'),
        'port': int(os.getenv('ES_PORT', '9200')),
        'username': os.getenv('ES_USERNAME'),
        'password': os.getenv('ES_PASSWORD'),
        'index': os.getenv('ES_INDEX')
    }

def get_es_client():
    """获取ES客户端"""
    try:
        es_config = get_es_config()
        # 使用URL中包含认证信息的方式，兼容阿里云ES
        es_url = f"http://{es_config['username']}:{es_config['password']}@{es_config['host']}:{es_config['port']}"

        # 从环境变量获取超时和重试配置
        timeout = int(os.getenv('ES_REQUEST_TIMEOUT', '15'))
        max_retries = int(os.getenv('ES_MAX_RETRIES', '3'))

        es_client = Elasticsearch(
            [es_url],
            timeout=timeout,
            max_retries=max_retries,
            retry_on_timeout=True
        )
        
        # 测试连接 - 使用更兼容的方法
        try:
            cluster_info = es_client.info()
            logger.info(f"ES连接成功: {cluster_info.get('cluster_name', 'unknown')}")
        except Exception as ping_error:
            logger.error(f"ES连接失败: {ping_error}")
            return None
            
        return es_client
        
    except Exception as e:
        logger.error(f"创建ES客户端失败: {e}")
        return None

@record_api.route('/api/record/<record_id>', methods=['GET'])
def get_record_detail(record_id):
    """获取单条记录的完整详情"""
    try:
        # 获取请求参数
        es_config = get_es_config()
        index_name = request.args.get('table', es_config['index'])
        
        # 获取ES客户端
        es_client = get_es_client()
        if not es_client:
            return jsonify({
                'success': False,
                'message': '无法连接到Elasticsearch',
                'data': None
            }), 500
        
        # 直接使用ES的get API获取完整记录
        try:
            response = es_client.get(index=index_name, id=record_id)
            record_data = response['_source']
            
            # 添加_id字段
            record_data['id'] = response['_id']
            
            # 确保保留所有原始字段，包括isAddToQueue
            
            return jsonify({
                'success': True,
                'message': '获取记录详情成功',
                'data': record_data
            })
            
        except Exception as e:
            logger.error(f"获取记录详情失败: {e}")
            logger.error(traceback.format_exc())
            
            # 尝试使用search API
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "match": {
                                    "_id": record_id
                                }
                            }
                        ]
                    }
                }
            }
            
            search_response = es_client.search(index=index_name, body=query)
            
            if search_response['hits']['total']['value'] > 0:
                record_data = search_response['hits']['hits'][0]['_source']
                record_data['id'] = search_response['hits']['hits'][0]['_id']
                
                return jsonify({
                    'success': True,
                    'message': '通过搜索获取记录详情成功',
                    'data': record_data
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'未找到ID为{record_id}的记录',
                    'data': None
                }), 404
            
    except Exception as e:
        logger.error(f"记录详情API错误: {e}")
        logger.error(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'message': f'获取记录详情失败: {str(e)}',
            'data': None
        }), 500
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
from typing import Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

@dataclass
class ESConfig:
    """Elasticsearch配置"""
    host: str
    port: int
    username: str
    password: str
    index: str
    timeout: int = 15
    max_retries: int = 3
    
    @classmethod
    def from_env(cls) -> 'ESConfig':
        """从环境变量创建配置"""
        # 检查必需的环境变量
        required_vars = ['ES_HOST', 'ES_USERNAME', 'ES_PASSWORD', 'ES_INDEX']
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")

        return cls(
            host=os.getenv('ES_HOST'),
            port=int(os.getenv('ES_PORT', '9200')),
            username=os.getenv('ES_USERNAME'),
            password=os.getenv('ES_PASSWORD'),
            index=os.getenv('ES_INDEX'),
            timeout=int(os.getenv('ES_TIMEOUT', '15')),
            max_retries=int(os.getenv('ES_MAX_RETRIES', '3'))
        )

@dataclass
class AppConfig:
    """应用配置"""
    host: str
    port: int
    debug: bool
    cache_ttl: int
    cache_size: int
    log_level: str
    
    @classmethod
    def from_env(cls) -> 'AppConfig':
        """从环境变量创建配置"""
        # 检查必需的环境变量
        required_vars = ['APP_HOST', 'APP_PORT']
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")

        return cls(
            host=os.getenv('APP_HOST'),
            port=int(os.getenv('APP_PORT')),
            debug=os.getenv('DEBUG', 'True').lower() == 'true',
            cache_ttl=int(os.getenv('CACHE_TTL', '300')),
            cache_size=int(os.getenv('CACHE_SIZE', '200')),
            log_level=os.getenv('LOG_LEVEL', 'INFO')
        )

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.es_config = ESConfig.from_env()
        self.app_config = AppConfig.from_env()
    
    def get_es_config_dict(self) -> Dict[str, Any]:
        """获取ES配置字典"""
        return {
            'host': self.es_config.host,
            'port': self.es_config.port,
            'username': self.es_config.username,
            'password': self.es_config.password,
            'index': self.es_config.index,
            'timeout': self.es_config.timeout,
            'max_retries': self.es_config.max_retries
        }
    
    def validate_config(self) -> bool:
        """验证配置"""
        errors = []
        
        # 验证ES配置
        if not self.es_config.host:
            errors.append("ES_HOST不能为空")
        if not self.es_config.username:
            errors.append("ES_USERNAME不能为空")
        if not self.es_config.password:
            errors.append("ES_PASSWORD不能为空")
        if not self.es_config.index:
            errors.append("ES_INDEX不能为空")
        
        # 验证应用配置
        if self.app_config.port < 1 or self.app_config.port > 65535:
            errors.append("APP_PORT必须在1-65535之间")
        
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        print("✅ 配置验证通过")
        return True

# 全局配置实例
config_manager = ConfigManager()